/* Font Awesome 6.0.0 核心样式 - 简化版本 */

/* 基础样式 */
.fa, .fas, .far, .fal, .fad, .fab {
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  line-height: 1;
}

.fas {
  font-family: "Font Awesome 6 Free";
  font-weight: 900;
}

.far {
  font-family: "Font Awesome 6 Free";
  font-weight: 400;
}

.fab {
  font-family: "Font Awesome 6 Brands";
  font-weight: 400;
}

/* 图标定义 - 使用Unicode字符作为替代 */
.fa-home:before { content: "🏠"; }
.fa-tools:before { content: "🔧"; }
.fa-list:before { content: "📋"; }
.fa-file-alt:before { content: "📄"; }
.fa-user:before { content: "👤"; }
.fa-cog:before { content: "⚙️"; }
.fa-info-circle:before { content: "ℹ️"; }
.fa-plus:before { content: "+"; }
.fa-edit:before { content: "✏️"; }
.fa-trash:before { content: "🗑️"; }
.fa-eye:before { content: "👁️"; }
.fa-download:before { content: "⬇️"; }
.fa-upload:before { content: "⬆️"; }
.fa-search:before { content: "🔍"; }
.fa-check:before { content: "✓"; }
.fa-times:before { content: "✕"; }
.fa-exclamation-triangle:before { content: "⚠️"; }
.fa-spinner:before { content: "⟳"; }
.fa-play:before { content: "▶️"; }
.fa-stop:before { content: "⏹️"; }
.fa-pause:before { content: "⏸️"; }
.fa-refresh:before { content: "🔄"; }
.fa-save:before { content: "💾"; }
.fa-folder:before { content: "📁"; }
.fa-folder-open:before { content: "📂"; }
.fa-calendar:before { content: "📅"; }
.fa-clock:before { content: "🕐"; }
.fa-bell:before { content: "🔔"; }
.fa-envelope:before { content: "✉️"; }
.fa-phone:before { content: "📞"; }
.fa-link:before { content: "🔗"; }
.fa-external-link-alt:before { content: "↗️"; }
.fa-arrow-up:before { content: "↑"; }
.fa-arrow-down:before { content: "↓"; }
.fa-arrow-left:before { content: "←"; }
.fa-arrow-right:before { content: "→"; }
.fa-chevron-up:before { content: "⌃"; }
.fa-chevron-down:before { content: "⌄"; }
.fa-chevron-left:before { content: "‹"; }
.fa-chevron-right:before { content: "›"; }
.fa-bars:before { content: "☰"; }
.fa-ellipsis-v:before { content: "⋮"; }
.fa-ellipsis-h:before { content: "⋯"; }
.fa-question:before { content: "?"; }
.fa-question-circle:before { content: "❓"; }
.fa-star:before { content: "⭐"; }
.fa-heart:before { content: "❤️"; }
.fa-thumbs-up:before { content: "👍"; }
.fa-thumbs-down:before { content: "👎"; }
.fa-comment:before { content: "💬"; }
.fa-comments:before { content: "💬"; }
.fa-share:before { content: "📤"; }
.fa-copy:before { content: "📋"; }
.fa-cut:before { content: "✂️"; }
.fa-paste:before { content: "📋"; }
.fa-undo:before { content: "↶"; }
.fa-redo:before { content: "↷"; }
.fa-print:before { content: "🖨️"; }
.fa-fax:before { content: "📠"; }
.fa-wifi:before { content: "📶"; }
.fa-bluetooth:before { content: "🔵"; }
.fa-battery-full:before { content: "🔋"; }
.fa-power-off:before { content: "⏻"; }
.fa-lock:before { content: "🔒"; }
.fa-unlock:before { content: "🔓"; }
.fa-key:before { content: "🔑"; }
.fa-shield-alt:before { content: "🛡️"; }
.fa-database:before { content: "🗄️"; }
.fa-server:before { content: "🖥️"; }
.fa-desktop:before { content: "🖥️"; }
.fa-laptop:before { content: "💻"; }
.fa-mobile:before { content: "📱"; }
.fa-tablet:before { content: "📱"; }
.fa-keyboard:before { content: "⌨️"; }
.fa-mouse:before { content: "🖱️"; }
.fa-gamepad:before { content: "🎮"; }
.fa-headphones:before { content: "🎧"; }
.fa-microphone:before { content: "🎤"; }
.fa-camera:before { content: "📷"; }
.fa-video:before { content: "📹"; }
.fa-film:before { content: "🎬"; }
.fa-music:before { content: "🎵"; }
.fa-volume-up:before { content: "🔊"; }
.fa-volume-down:before { content: "🔉"; }
.fa-volume-mute:before { content: "🔇"; }

/* 尺寸类 */
.fa-xs { font-size: 0.75em; }
.fa-sm { font-size: 0.875em; }
.fa-lg { font-size: 1.33333em; line-height: 0.75em; vertical-align: -0.0667em; }
.fa-xl { font-size: 1.5em; line-height: 0.6667em; vertical-align: -0.075em; }
.fa-2x { font-size: 2em; }
.fa-3x { font-size: 3em; }
.fa-4x { font-size: 4em; }
.fa-5x { font-size: 5em; }
.fa-6x { font-size: 6em; }
.fa-7x { font-size: 7em; }
.fa-8x { font-size: 8em; }
.fa-9x { font-size: 9em; }
.fa-10x { font-size: 10em; }

/* 固定宽度 */
.fa-fw {
  text-align: center;
  width: 1.25em;
}

/* 旋转和翻转 */
.fa-rotate-90 { transform: rotate(90deg); }
.fa-rotate-180 { transform: rotate(180deg); }
.fa-rotate-270 { transform: rotate(270deg); }
.fa-flip-horizontal { transform: scale(-1, 1); }
.fa-flip-vertical { transform: scale(1, -1); }
.fa-flip-both { transform: scale(-1, -1); }

/* 动画 */
.fa-spin {
  animation: fa-spin 2s infinite linear;
}

.fa-pulse {
  animation: fa-spin 1s infinite steps(8);
}

@keyframes fa-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 边框和拉取 */
.fa-border {
  border: solid 0.08em #eee;
  border-radius: 0.1em;
  padding: 0.2em 0.25em 0.15em;
}

.fa-pull-left {
  float: left;
  margin-right: 0.3em;
}

.fa-pull-right {
  float: right;
  margin-left: 0.3em;
}

/* 堆叠 */
.fa-stack {
  display: inline-block;
  height: 2em;
  line-height: 2em;
  position: relative;
  vertical-align: middle;
  width: 2.5em;
}

.fa-stack-1x, .fa-stack-2x {
  left: 0;
  position: absolute;
  text-align: center;
  width: 100%;
}

.fa-stack-1x { line-height: inherit; }
.fa-stack-2x { font-size: 2em; }

/* 列表 */
.fa-ul {
  list-style-type: none;
  margin-left: 2.5em;
  padding-left: 0;
}

.fa-ul > li {
  position: relative;
}

.fa-li {
  left: -2em;
  position: absolute;
  text-align: center;
  width: 2em;
  line-height: inherit;
}

/* 屏幕阅读器 */
.sr-only {
  border: 0;
  clip: rect(0, 0, 0, 0);
  height: 1px;
  margin: -1px;
  overflow: hidden;
  padding: 0;
  position: absolute;
  width: 1px;
}

.sr-only-focusable:active, .sr-only-focusable:focus {
  clip: auto;
  height: auto;
  margin: 0;
  overflow: visible;
  position: static;
  width: auto;
}

/* 品牌图标 */
.fa-github:before { content: "🐙"; }
.fa-twitter:before { content: "🐦"; }
.fa-facebook:before { content: "📘"; }
.fa-linkedin:before { content: "💼"; }
.fa-instagram:before { content: "📷"; }
.fa-youtube:before { content: "📺"; }
.fa-google:before { content: "🔍"; }
.fa-apple:before { content: "🍎"; }
.fa-microsoft:before { content: "🪟"; }
.fa-amazon:before { content: "📦"; }

/* 特殊图标 */
.fa-code:before { content: "</>" }
.fa-terminal:before { content: ">_" }
.fa-bug:before { content: "🐛"; }
.fa-wrench:before { content: "🔧"; }
.fa-hammer:before { content: "🔨"; }
.fa-screwdriver:before { content: "🪛"; }
.fa-gear:before { content: "⚙️"; }
.fa-cogs:before { content: "⚙️⚙️"; }

/* 状态图标 */
.fa-check-circle:before { content: "✅"; }
.fa-times-circle:before { content: "❌"; }
.fa-exclamation-circle:before { content: "❗"; }
.fa-info:before { content: "ℹ️"; }
.fa-warning:before { content: "⚠️"; }
.fa-ban:before { content: "🚫"; }

/* 文件类型图标 */
.fa-file:before { content: "📄"; }
.fa-file-text:before { content: "📄"; }
.fa-file-pdf:before { content: "📕"; }
.fa-file-word:before { content: "📘"; }
.fa-file-excel:before { content: "📗"; }
.fa-file-powerpoint:before { content: "📙"; }
.fa-file-image:before { content: "🖼️"; }
.fa-file-video:before { content: "📹"; }
.fa-file-audio:before { content: "🎵"; }
.fa-file-archive:before { content: "🗜️"; }
.fa-file-code:before { content: "📝"; }

/* 网络和通信 */
.fa-globe:before { content: "🌐"; }
.fa-rss:before { content: "📡"; }
.fa-signal:before { content: "📶"; }
.fa-broadcast-tower:before { content: "📡"; }
.fa-satellite:before { content: "🛰️"; }
.fa-antenna:before { content: "📡"; }

/* 交通工具 */
.fa-car:before { content: "🚗"; }
.fa-truck:before { content: "🚚"; }
.fa-bus:before { content: "🚌"; }
.fa-train:before { content: "🚆"; }
.fa-plane:before { content: "✈️"; }
.fa-ship:before { content: "🚢"; }
.fa-bicycle:before { content: "🚲"; }
.fa-motorcycle:before { content: "🏍️"; }

/* 天气 */
.fa-sun:before { content: "☀️"; }
.fa-moon:before { content: "🌙"; }
.fa-cloud:before { content: "☁️"; }
.fa-rain:before { content: "🌧️"; }
.fa-snow:before { content: "❄️"; }
.fa-bolt:before { content: "⚡"; }
.fa-rainbow:before { content: "🌈"; }

/* 表情符号 */
.fa-smile:before { content: "😊"; }
.fa-frown:before { content: "😞"; }
.fa-meh:before { content: "😐"; }
.fa-laugh:before { content: "😂"; }
.fa-angry:before { content: "😠"; }
.fa-surprise:before { content: "😲"; }
.fa-wink:before { content: "😉"; }
