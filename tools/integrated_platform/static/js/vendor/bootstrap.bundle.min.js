/*!
 * Bootstrap v5.3.0 (https://getbootstrap.com/)
 * Copyright 2011-2023 The Bootstrap Authors (https://github.com/twbs/bootstrap/graphs/contributors)
 * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)
 * 
 * 简化版本 - 只包含基本功能
 */

(function() {
    'use strict';

    // 基本的Bootstrap功能实现
    
    // Alert 关闭功能
    document.addEventListener('DOMContentLoaded', function() {
        // Alert dismiss
        const alertCloseButtons = document.querySelectorAll('[data-bs-dismiss="alert"]');
        alertCloseButtons.forEach(function(button) {
            button.addEventListener('click', function() {
                const alert = button.closest('.alert');
                if (alert) {
                    alert.style.opacity = '0';
                    setTimeout(function() {
                        alert.remove();
                    }, 150);
                }
            });
        });

        // Collapse 功能
        const collapseToggles = document.querySelectorAll('[data-bs-toggle="collapse"]');
        collapseToggles.forEach(function(toggle) {
            toggle.addEventListener('click', function(e) {
                e.preventDefault();
                const target = document.querySelector(toggle.getAttribute('data-bs-target'));
                if (target) {
                    if (target.classList.contains('show')) {
                        target.classList.remove('show');
                        target.style.height = '0';
                    } else {
                        target.classList.add('show');
                        target.style.height = target.scrollHeight + 'px';
                    }
                }
            });
        });

        // 移动端导航切换
        const mobileNavToggle = document.getElementById('mobileNavToggle');
        const sidebar = document.getElementById('sidebar');
        
        if (mobileNavToggle && sidebar) {
            mobileNavToggle.addEventListener('click', function() {
                sidebar.classList.toggle('show');
            });
        }

        const sidebarToggle = document.getElementById('sidebarToggle');
        if (sidebarToggle && sidebar) {
            sidebarToggle.addEventListener('click', function() {
                sidebar.classList.remove('show');
            });
        }
    });

    // 简单的工具函数
    window.Bootstrap = {
        Alert: {
            close: function(element) {
                if (element) {
                    element.style.opacity = '0';
                    setTimeout(function() {
                        element.remove();
                    }, 150);
                }
            }
        },
        Collapse: {
            toggle: function(element) {
                if (element) {
                    if (element.classList.contains('show')) {
                        element.classList.remove('show');
                        element.style.height = '0';
                    } else {
                        element.classList.add('show');
                        element.style.height = element.scrollHeight + 'px';
                    }
                }
            }
        }
    };

})();
