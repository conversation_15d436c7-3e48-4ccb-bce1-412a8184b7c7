#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
报文修改插件

该插件提供网络报文（PCAP文件）的IP地址修改功能，支持批量处理多个PCAP文件，
并提供文件上传、处理、下载和自动清理等完整的工作流程。

主要功能：
1. PCAP文件上传和验证
2. 批量修改报文中的源IP和目标IP地址
3. 处理结果打包下载
4. 历史文件自动清理
5. 任务状态查询和监控

作者: LiaoJunBO
版本: 1.0.0
"""

from typing import Dict, List, Any, Optional, Tuple
from flask import render_template, request, jsonify, send_file
from core.base_plugin import BasePlugin
from .pcapChange import pcapChange
import os
import tarfile
import uuid
import shutil
import time
from datetime import datetime, timedelta
from werkzeug.utils import secure_filename


class Plugin(BasePlugin):
    """
    报文修改插件主类

    继承自BasePlugin，提供PCAP文件的IP地址修改功能。
    支持文件上传、批量处理、结果下载和自动清理等功能。
    """

    def __init__(self, **kwargs):
        """
        初始化插件实例

        Args:
            **kwargs: 插件初始化参数，传递给父类BasePlugin

        功能：
        - 设置插件基本信息（名称、版本、作者等）
        - 初始化pcapChange处理器实例
        - 创建必要的工作目录（uploads、output）
        - 配置文件清理策略参数
        """
        super().__init__(**kwargs)

        # 设置插件基本信息
        self.name = "pcapChange"
        self.displayName = "报文修改工具"
        self.description = "用于批量修改网络报文IP地址的工具插件"
        self.version = "1.0.0"
        self.author = "LiaoJunBO"

        # 初始化pcapChange处理器实例
        self.pcapProcessor = pcapChange()

        # 创建工作目录
        self.uploadDir = os.path.join(os.path.dirname(__file__), 'uploads')
        self.outputDir = os.path.join(os.path.dirname(__file__), 'output')
        os.makedirs(self.uploadDir, exist_ok=True)
        os.makedirs(self.outputDir, exist_ok=True)

        # 文件清理策略配置
        self.maxFileAge = 24 * 60 * 60  # 文件最大保留时间：24小时（秒）
        self.maxTaskCount = 10  # 最大保留任务数量：10个
        
    def getRoutes(self) -> List[Dict[str, Any]]:
        """
        获取页面路由配置

        Returns:
            List[Dict[str, Any]]: 页面路由配置列表，每个字典包含：
                - rule: URL规则
                - view_func: 视图函数
                - methods: 支持的HTTP方法
                - endpoint: 端点名称

        功能：
        - 配置插件的前端页面路由
        - 包括主页面和测试页面
        """
        return [
            {
                "rule": "/",
                "view_func": self.indexPage,
                "methods": ["GET"],
                "endpoint": "index"
            },
            {
                "rule": "/test",
                "view_func": self.testPage,
                "methods": ["GET"],
                "endpoint": "test"
            }
        ]

    def getApiRoutes(self) -> List[Dict[str, Any]]:
        """
        获取API路由配置

        Returns:
            List[Dict[str, Any]]: API路由配置列表，每个字典包含：
                - rule: URL规则
                - view_func: 视图函数
                - methods: 支持的HTTP方法
                - endpoint: 端点名称

        功能：
        - 配置插件的API接口路由
        - 包括文件上传、处理、下载、状态查询和清理等功能
        """
        return [
            {
                "rule": "/upload",
                "view_func": self.apiUploadFiles,
                "methods": ["POST"],
                "endpoint": "upload"
            },
            {
                "rule": "/process",
                "view_func": self.apiProcessFiles,
                "methods": ["POST"],
                "endpoint": "process"
            },
            {
                "rule": "/download/<taskId>",
                "view_func": self.apiDownloadResult,
                "methods": ["GET"],
                "endpoint": "download"
            },
            {
                "rule": "/status/<taskId>",
                "view_func": self.apiGetStatus,
                "methods": ["GET"],
                "endpoint": "status"
            },
            {
                "rule": "/cleanup",
                "view_func": self.apiCleanupFiles,
                "methods": ["POST"],
                "endpoint": "cleanup"
            }
        ]

    def getNavItems(self) -> List[Dict[str, Any]]:
        """
        获取导航项配置

        Returns:
            List[Dict[str, Any]]: 导航项配置列表，每个字典包含：
                - title: 导航标题
                - url: 导航链接
                - icon: 图标类名
                - order: 排序权重

        功能：
        - 配置插件在主导航菜单中的显示
        - 设置图标、标题和排序位置
        """
        return [
            {
                "title": "报文修改",
                "url": "/plugins/pcapChange/",
                "icon": "fas fa-network-wired",
                "order": 20
            }
        ]

    def onInitialize(self):
        """
        插件初始化回调方法

        功能：
        - 在插件加载完成后执行
        - 记录初始化完成日志
        - 可用于执行额外的初始化操作
        """
        self.logger.info("报文修改插件初始化完成")
        
    def indexPage(self):
        """
        插件主页面视图

        Returns:
            str: 渲染后的HTML页面内容
            tuple: 错误情况下返回(错误信息, HTTP状态码)

        功能：
        - 渲染插件的主要操作界面
        - 提供文件上传、处理配置、结果下载等功能
        - 传递插件信息到模板中
        """
        try:
            return render_template('pcapChange/index.html',
                                 pluginInfo=self.getInfo())

        except Exception as e:
            self.logger.error(f"加载报文修改插件页面失败: {str(e)}")
            return f"加载页面失败: {str(e)}", 500

    def testPage(self):
        """
        测试页面视图

        Returns:
            str: 渲染后的HTML页面内容
            tuple: 错误情况下返回(错误信息, HTTP状态码)

        功能：
        - 提供简化的测试界面
        - 用于功能验证和调试
        """
        try:
            return render_template('pcapChange/test.html')
        except Exception as e:
            self.logger.error(f"加载测试页面失败: {str(e)}")
            return f"加载测试页面失败: {str(e)}", 500
            
    def apiUploadFiles(self):
        """
        文件上传API接口

        HTTP方法: POST
        请求格式: multipart/form-data
        请求参数:
            files: 上传的文件列表（支持多文件上传）

        Returns:
            JSON响应:
            成功时:
            {
                "success": True,
                "data": {
                    "taskId": "生成的任务ID",
                    "files": [
                        {
                            "name": "文件名",
                            "path": "文件路径",
                            "size": 文件大小
                        }
                    ]
                },
                "message": "成功上传X个文件"
            }
            失败时:
            {
                "success": False,
                "message": "错误信息"
            }

        功能：
        - 自动清理历史文件（开始新任务前）
        - 验证上传文件的类型（仅支持.pcap, .cap, .pcapng）
        - 生成唯一的任务ID
        - 安全地保存上传的文件
        - 记录上传操作日志
        """
        try:
            # 开始新任务前先清理历史文件
            self._cleanupOldFiles()

            # 验证请求中是否包含文件
            if 'files' not in request.files:
                return jsonify({
                    "success": False,
                    "message": "没有选择文件"
                }), 400

            files = request.files.getlist('files')
            if not files or all(f.filename == '' for f in files):
                return jsonify({
                    "success": False,
                    "message": "没有选择有效文件"
                }), 400

            # 生成唯一任务ID并创建任务目录
            taskId = str(uuid.uuid4())
            taskDir = os.path.join(self.uploadDir, taskId)
            os.makedirs(taskDir, exist_ok=True)

            uploadedFiles = []
            for file in files:
                if file and file.filename:
                    # 验证文件类型（仅支持PCAP格式）
                    if not file.filename.lower().endswith(('.pcap', '.cap', '.pcapng')):
                        return jsonify({
                            "success": False,
                            "message": f"不支持的文件类型: {file.filename}，只支持.pcap, .cap, .pcapng文件"
                        }), 400

                    # 安全处理文件名并保存文件
                    filename = secure_filename(file.filename)
                    filePath = os.path.join(taskDir, filename)
                    file.save(filePath)
                    uploadedFiles.append({
                        "name": filename,
                        "path": filePath,
                        "size": os.path.getsize(filePath)
                    })

            self.logger.info(f"任务 {taskId} 上传了 {len(uploadedFiles)} 个文件")

            return jsonify({
                "success": True,
                "data": {
                    "taskId": taskId,
                    "files": uploadedFiles
                },
                "message": f"成功上传 {len(uploadedFiles)} 个文件"
            })

        except Exception as e:
            self.logger.error(f"文件上传失败: {str(e)}")
            return jsonify({
                "success": False,
                "message": f"文件上传失败: {str(e)}"
            }), 500
            
    def apiProcessFiles(self):
        """
        文件处理API接口

        HTTP方法: POST
        请求格式: application/json
        请求参数:
            taskId (str): 任务ID（由上传接口返回）
            srcIp (str): 源IP地址（格式：xxx.xxx.xxx.xxx）
            dstIp (str): 目标IP地址（格式：xxx.xxx.xxx.xxx）

        Returns:
            JSON响应:
            成功时:
            {
                "success": True,
                "data": {
                    "taskId": "任务ID",
                    "processedCount": 处理的文件数量,
                    "downloadUrl": "下载链接",
                    "tarPath": "压缩包路径"
                },
                "message": "成功处理X个报文文件"
            }
            失败时:
            {
                "success": False,
                "message": "错误信息"
            }

        功能：
        - 验证请求参数的完整性和格式
        - 检查任务是否存在
        - 批量处理PCAP文件，修改IP地址
        - 创建处理结果的压缩包
        - 支持IP地址自增长（多文件时）
        """
        try:
            # 获取并验证请求数据
            data = request.get_json()
            if not data:
                return jsonify({
                    "success": False,
                    "message": "缺少请求数据"
                }), 400

            # 验证必需参数
            required_params = ['taskId', 'srcIp', 'dstIp']
            for param in required_params:
                if param not in data:
                    return jsonify({
                        "success": False,
                        "message": f"缺少必需参数: {param}"
                    }), 400

            taskId = data['taskId']
            srcIp = data['srcIp']
            dstIp = data['dstIp']

            # 验证IP地址格式
            if not self._validateIpAddress(srcIp):
                return jsonify({
                    "success": False,
                    "message": f"源IP地址格式不正确: {srcIp}"
                }), 400

            if not self._validateIpAddress(dstIp):
                return jsonify({
                    "success": False,
                    "message": f"目标IP地址格式不正确: {dstIp}"
                }), 400

            # 检查任务目录是否存在
            taskDir = os.path.join(self.uploadDir, taskId)
            if not os.path.exists(taskDir):
                return jsonify({
                    "success": False,
                    "message": "任务不存在或已过期"
                }), 404

            # 获取任务目录中的PCAP文件列表
            pcapFiles = []
            for filename in os.listdir(taskDir):
                if filename.lower().endswith(('.pcap', '.cap', '.pcapng')):
                    pcapFiles.append(os.path.join(taskDir, filename))

            if not pcapFiles:
                return jsonify({
                    "success": False,
                    "message": "没有找到可处理的报文文件"
                }), 400

            # 批量处理PCAP文件
            processedFiles = self._processPcapFiles(pcapFiles, srcIp, dstIp, taskId)

            if not processedFiles:
                return jsonify({
                    "success": False,
                    "message": "报文处理失败"
                }), 500

            # 创建处理结果的压缩包
            tarPath = self._createTarArchive(processedFiles, taskId)

            if not tarPath:
                return jsonify({
                    "success": False,
                    "message": "创建压缩包失败"
                }), 500

            return jsonify({
                "success": True,
                "data": {
                    "taskId": taskId,
                    "processedCount": len(processedFiles),
                    "downloadUrl": f"/plugins/pcapChange/download/{taskId}",
                    "tarPath": tarPath
                },
                "message": f"成功处理 {len(processedFiles)} 个报文文件"
            })

        except Exception as e:
            self.logger.error(f"处理文件失败: {str(e)}")
            return jsonify({
                "success": False,
                "message": f"处理文件失败: {str(e)}"
            }), 500

    def apiDownloadResult(self, taskId):
        """
        下载处理结果API接口

        HTTP方法: GET
        URL参数:
            taskId (str): 任务ID

        Returns:
            成功时: 返回压缩包文件流（application/gzip）
            失败时: JSON错误响应
            {
                "success": False,
                "message": "错误信息"
            }

        功能：
        - 根据任务ID查找对应的处理结果压缩包
        - 以附件形式返回文件供用户下载
        - 设置合适的文件名和MIME类型
        """
        try:
            tarPath = os.path.join(self.outputDir, f"{taskId}_processed.tar.gz")

            if not os.path.exists(tarPath):
                return jsonify({
                    "success": False,
                    "message": "文件不存在或已过期"
                }), 404

            return send_file(
                tarPath,
                as_attachment=True,
                download_name=f"processed_pcaps_{taskId}.tar.gz",
                mimetype='application/gzip'
            )

        except Exception as e:
            self.logger.error(f"下载文件失败: {str(e)}")
            return jsonify({
                "success": False,
                "message": f"下载文件失败: {str(e)}"
            }), 500

    def apiGetStatus(self, taskId):
        """
        获取任务状态API接口

        HTTP方法: GET
        URL参数:
            taskId (str): 任务ID

        Returns:
            JSON响应:
            {
                "success": True,
                "data": {
                    "taskId": "任务ID",
                    "uploaded": 是否已上传文件,
                    "processed": 是否已处理完成,
                    "downloadReady": 是否可以下载,
                    "fileCount": 上传的文件数量（如果已上传）,
                    "fileSize": 结果文件大小（如果已处理）
                }
            }

        功能：
        - 查询指定任务的当前状态
        - 检查文件上传、处理、下载各阶段的完成情况
        - 提供文件数量和大小等详细信息
        """
        try:
            taskDir = os.path.join(self.uploadDir, taskId)
            tarPath = os.path.join(self.outputDir, f"{taskId}_processed.tar.gz")

            status = {
                "taskId": taskId,
                "uploaded": os.path.exists(taskDir),
                "processed": os.path.exists(tarPath),
                "downloadReady": os.path.exists(tarPath)
            }

            # 如果文件已上传，统计文件数量
            if status["uploaded"]:
                files = [f for f in os.listdir(taskDir)
                        if f.lower().endswith(('.pcap', '.cap', '.pcapng'))]
                status["fileCount"] = len(files)

            # 如果已处理完成，获取结果文件大小
            if status["processed"]:
                status["fileSize"] = os.path.getsize(tarPath)

            return jsonify({
                "success": True,
                "data": status
            })

        except Exception as e:
            self.logger.error(f"获取任务状态失败: {str(e)}")
            return jsonify({
                "success": False,
                "message": f"获取任务状态失败: {str(e)}"
            }), 500

    def _validateIpAddress(self, ip):
        """
        验证IP地址格式

        Args:
            ip (str): 待验证的IP地址字符串

        Returns:
            bool: IP地址格式是否正确

        功能：
        - 验证IP地址是否为标准的IPv4格式（xxx.xxx.xxx.xxx）
        - 检查每个段是否为0-255范围内的数字
        """
        try:
            parts = ip.split('.')
            if len(parts) != 4:
                return False
            for part in parts:
                if not part.isdigit() or not (0 <= int(part) <= 255):
                    return False
            return True
        except:
            return False

    def _processPcapFiles(self, pcapFiles, srcIp, dstIp, taskId):
        """
        批量处理PCAP文件

        Args:
            pcapFiles (List[str]): PCAP文件路径列表
            srcIp (str): 源IP地址
            dstIp (str): 目标IP地址
            taskId (str): 任务ID（用于日志记录）

        Returns:
            List[str]: 处理成功的输出文件路径列表

        功能：
        - 逐个处理PCAP文件，修改其中的IP地址
        - 支持IP地址自增长（多文件时每个文件使用不同的IP）
        - 记录处理进度和结果
        - 处理失败时继续处理其他文件
        """
        try:
            processedFiles = []
            count = 0

            for pcapFile in pcapFiles:
                count += 1
                self.logger.info(f"处理文件 {count}/{len(pcapFiles)}: {os.path.basename(pcapFile)}")

                # 使用IP地址自增长功能（多文件时每个文件使用不同的IP）
                currentSrcIp = self.pcapProcessor._generate_ip_address(srcIp, count - 1)
                currentDstIp = self.pcapProcessor._generate_ip_address(dstIp, count - 1)

                # 调用pcapChange处理器的rewrite方法
                outputFile = self.pcapProcessor.rewrite(
                    srcIp=currentSrcIp,
                    dstIp=currentDstIp,
                    pcapPath=pcapFile
                )

                if outputFile and os.path.exists(outputFile):
                    processedFiles.append(outputFile)
                    self.logger.info(f"成功处理文件: {os.path.basename(pcapFile)} -> {os.path.basename(outputFile)}")
                else:
                    self.logger.error(f"处理文件失败: {os.path.basename(pcapFile)}")

            return processedFiles

        except Exception as e:
            self.logger.error(f"批量处理文件失败: {str(e)}")
            return []

    def _createTarArchive(self, processedFiles, taskId):
        """
        创建处理结果的TAR压缩包

        Args:
            processedFiles (List[str]): 处理后的文件路径列表
            taskId (str): 任务ID，用于生成压缩包文件名

        Returns:
            str: 压缩包文件路径，失败时返回None

        功能：
        - 将所有处理后的PCAP文件打包成tar.gz格式
        - 使用任务ID作为压缩包文件名
        - 自动清理临时的处理文件和缓存文件
        - 在压缩包中使用简化的文件名（仅保留文件名部分）

        清理策略：
        - 清理处理后的_out.pcap文件
        - 清理对应的.cache缓存文件
        - 清理失败时记录警告但不影响压缩包创建
        """
        try:
            tarPath = os.path.join(self.outputDir, f"{taskId}_processed.tar.gz")

            with tarfile.open(tarPath, 'w:gz') as tar:
                for filePath in processedFiles:
                    if os.path.exists(filePath):
                        # 使用文件名作为压缩包内的路径，避免包含完整目录结构
                        arcname = os.path.basename(filePath)
                        tar.add(filePath, arcname=arcname)
                        self.logger.info(f"添加文件到压缩包: {arcname}")

            # 清理处理后的临时文件
            for filePath in processedFiles:
                try:
                    if os.path.exists(filePath):
                        os.remove(filePath)
                        # 同时删除可能的cache文件
                        cacheFile = filePath.replace('_out.pcap', '.cache')
                        if os.path.exists(cacheFile):
                            os.remove(cacheFile)
                except Exception as e:
                    self.logger.warning(f"清理临时文件失败: {filePath}, 错误: {str(e)}")

            self.logger.info(f"成功创建压缩包: {tarPath}")
            return tarPath

        except Exception as e:
            self.logger.error(f"创建压缩包失败: {str(e)}")
            return None

    def apiCleanupFiles(self):
        """
        手动清理文件API接口

        HTTP方法: POST
        请求格式: application/json
        请求参数:
            force (bool): 是否强制清理所有文件，默认为False

        Returns:
            JSON响应:
            {
                "success": True,
                "data": {
                    "cleanedCount": 清理的任务数量
                },
                "message": "清理结果描述"
            }

        功能：
        - 提供手动触发文件清理的API接口
        - 支持按规则清理和强制清理两种模式
        - 返回详细的清理统计信息

        清理模式：
        - force=False: 按配置的规则清理（保留最新任务，删除过期任务）
        - force=True: 强制清理所有任务文件（慎用）
        """
        try:
            data = request.get_json() or {}
            forceCleanup = data.get('force', False)

            if forceCleanup:
                # 强制清理所有文件
                cleanedCount = self._cleanupAllFiles()
                message = f"强制清理完成，删除了 {cleanedCount} 个任务的文件"
            else:
                # 按规则清理旧文件
                cleanedCount = self._cleanupOldFiles()
                message = f"清理完成，删除了 {cleanedCount} 个过期任务的文件"

            return jsonify({
                "success": True,
                "data": {
                    "cleanedCount": cleanedCount
                },
                "message": message
            })

        except Exception as e:
            self.logger.error(f"清理文件失败: {str(e)}")
            return jsonify({
                "success": False,
                "message": f"清理文件失败: {str(e)}"
            }), 500

    def _cleanupOldFiles(self):
        """清理过期的任务文件"""
        try:
            cleanedCount = 0
            currentTime = time.time()

            # 获取所有任务，按时间排序
            tasks = self._getAllTasks()

            # 按修改时间排序，最新的在前
            tasks.sort(key=lambda x: x['mtime'], reverse=True)

            # 清理策略：
            # 1. 保留最新的 maxTaskCount 个任务
            # 2. 删除超过 maxFileAge 的任务

            for i, task in enumerate(tasks):
                shouldDelete = False
                reason = ""

                # 检查是否超过最大保留数量
                if i >= self.maxTaskCount:
                    shouldDelete = True
                    reason = f"超过最大保留数量({self.maxTaskCount})"

                # 检查是否超过最大保留时间
                elif (currentTime - task['mtime']) > self.maxFileAge:
                    shouldDelete = True
                    age_hours = (currentTime - task['mtime']) / 3600
                    reason = f"文件过期({age_hours:.1f}小时)"

                if shouldDelete:
                    if self._deleteTaskFiles(task['taskId']):
                        cleanedCount += 1
                        self.logger.info(f"清理任务 {task['taskId']}: {reason}")
                    else:
                        self.logger.warning(f"清理任务失败: {task['taskId']}")

            if cleanedCount > 0:
                self.logger.info(f"自动清理完成，删除了 {cleanedCount} 个过期任务")

            return cleanedCount

        except Exception as e:
            self.logger.error(f"清理过期文件失败: {str(e)}")
            return 0

    def _cleanupAllFiles(self):
        """强制清理所有任务文件"""
        try:
            cleanedCount = 0
            tasks = self._getAllTasks()

            for task in tasks:
                if self._deleteTaskFiles(task['taskId']):
                    cleanedCount += 1
                    self.logger.info(f"强制清理任务: {task['taskId']}")
                else:
                    self.logger.warning(f"强制清理任务失败: {task['taskId']}")

            self.logger.info(f"强制清理完成，删除了 {cleanedCount} 个任务")
            return cleanedCount

        except Exception as e:
            self.logger.error(f"强制清理文件失败: {str(e)}")
            return 0

    def _getAllTasks(self):
        """获取所有任务信息"""
        try:
            tasks = []

            # 扫描上传目录
            if os.path.exists(self.uploadDir):
                for item in os.listdir(self.uploadDir):
                    itemPath = os.path.join(self.uploadDir, item)
                    if os.path.isdir(itemPath):
                        # 检查是否是有效的UUID格式
                        try:
                            uuid.UUID(item)
                            mtime = os.path.getmtime(itemPath)
                            tasks.append({
                                'taskId': item,
                                'mtime': mtime,
                                'uploadDir': itemPath,
                                'outputFile': os.path.join(self.outputDir, f"{item}_processed.tar.gz")
                            })
                        except ValueError:
                            # 不是有效的UUID，跳过
                            continue

            return tasks

        except Exception as e:
            self.logger.error(f"获取任务列表失败: {str(e)}")
            return []

    def _deleteTaskFiles(self, taskId):
        """删除指定任务的所有文件"""
        try:
            success = True

            # 删除上传目录
            uploadPath = os.path.join(self.uploadDir, taskId)
            if os.path.exists(uploadPath):
                try:
                    shutil.rmtree(uploadPath)
                    self.logger.debug(f"删除上传目录: {uploadPath}")
                except Exception as e:
                    self.logger.error(f"删除上传目录失败 {uploadPath}: {str(e)}")
                    success = False

            # 删除输出文件
            outputPath = os.path.join(self.outputDir, f"{taskId}_processed.tar.gz")
            if os.path.exists(outputPath):
                try:
                    os.remove(outputPath)
                    self.logger.debug(f"删除输出文件: {outputPath}")
                except Exception as e:
                    self.logger.error(f"删除输出文件失败 {outputPath}: {str(e)}")
                    success = False

            # 删除可能的临时文件
            tempPattern = f"{taskId}_"
            for root, _, files in os.walk(os.path.dirname(__file__)):
                for file in files:
                    if file.startswith(tempPattern):
                        tempPath = os.path.join(root, file)
                        try:
                            os.remove(tempPath)
                            self.logger.debug(f"删除临时文件: {tempPath}")
                        except Exception as e:
                            self.logger.warning(f"删除临时文件失败 {tempPath}: {str(e)}")

            return success

        except Exception as e:
            self.logger.error(f"删除任务文件失败 {taskId}: {str(e)}")
            return False

    def getTasksInfo(self):
        """获取任务信息（用于调试和监控）"""
        try:
            tasks = self._getAllTasks()
            currentTime = time.time()

            tasksInfo = []
            for task in tasks:
                age_hours = (currentTime - task['mtime']) / 3600
                uploadSize = self._getDirSize(task['uploadDir']) if os.path.exists(task['uploadDir']) else 0
                outputSize = os.path.getsize(task['outputFile']) if os.path.exists(task['outputFile']) else 0

                tasksInfo.append({
                    'taskId': task['taskId'],
                    'ageHours': round(age_hours, 2),
                    'uploadSize': uploadSize,
                    'outputSize': outputSize,
                    'totalSize': uploadSize + outputSize,
                    'hasUpload': os.path.exists(task['uploadDir']),
                    'hasOutput': os.path.exists(task['outputFile'])
                })

            # 按年龄排序
            tasksInfo.sort(key=lambda x: x['ageHours'])

            return {
                'totalTasks': len(tasksInfo),
                'totalSize': sum(t['totalSize'] for t in tasksInfo),
                'tasks': tasksInfo
            }

        except Exception as e:
            self.logger.error(f"获取任务信息失败: {str(e)}")
            return {'totalTasks': 0, 'totalSize': 0, 'tasks': []}

    def _getDirSize(self, dirPath):
        """计算目录大小"""
        try:
            totalSize = 0
            for dirpath, _, filenames in os.walk(dirPath):
                for filename in filenames:
                    filepath = os.path.join(dirpath, filename)
                    if os.path.exists(filepath):
                        totalSize += os.path.getsize(filepath)
            return totalSize
        except Exception:
            return 0
