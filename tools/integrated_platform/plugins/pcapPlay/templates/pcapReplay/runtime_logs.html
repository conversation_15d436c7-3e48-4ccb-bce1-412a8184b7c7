{% extends "base.html" %}

{% block title %}运行日志 - {{ pluginInfo.displayName }}{% endblock %}

{% block extra_css %}
<!-- 引入通用样式框架 -->
<link href="{{ url_for('static', filename='plugins/pcapPlay/css/pcap-replay-common.css') }}" rel="stylesheet">
{% endblock %}

{% block content %}
<div class="pcap-replay-container">
    <!-- 页面头部 -->
    <div class="page-header">
        <div class="header-content">
            <div class="header-title">
                <i class="fas fa-play-circle me-3"></i>
            </div>
            <div class="header-actions">
                <a class="action-btn" href="/plugins/pcapPlay/">
                    <i class="fas fa-cog"></i>
                    <span>系统设置</span>
                </a>
                <a class="action-btn" href="/plugins/pcapPlay/create-task">
                    <i class="fas fa-plus-circle"></i>
                    <span>新建任务</span>
                </a>
                <a class="action-btn" href="/plugins/pcapPlay/task-results">
                    <i class="fas fa-list-alt"></i>
                    <span>任务结果</span>
                </a>
                <a class="action-btn active" href="/plugins/pcapPlay/runtime-logs">
                    <i class="fas fa-file-alt"></i>
                    <span>运行日志</span>
                </a>
            </div>
        </div>
    </div>

    <!-- 运行日志面板 -->
    <div class="content-panels">
        <div class="panel runtime-logs-panel">
            <div class="panel-header">
                <div class="panel-icon">
                    <i class="fas fa-file-alt"></i>
                </div>
                <div class="panel-title">
                    <h3>运行日志</h3>
                    <p>查看报文回放功能的运行日志信息（最新500条）</p>
                </div>
                <div class="panel-actions">
                    <!-- 过滤控件 -->
                    <div class="filter-controls">
                        <div class="filter-group">
                            <label class="filter-label">级别:</label>
                            <select id="levelFilter" class="filter-select" onchange="applyFilters()">
                                <option value="">全部</option>
                                <option value="DEBUG">DEBUG</option>
                                <option value="INFO">INFO</option>
                                <option value="WARN">WARN</option>
                                <option value="ERROR">ERROR</option>
                            </select>
                        </div>
                        <div class="filter-group">
                            <label class="filter-label">任务ID:</label>
                            <select id="taskIdFilter" class="filter-select" onchange="applyFilters()">
                                <option value="">全部</option>
                                <!-- 动态填充任务ID选项 -->
                            </select>
                        </div>
                        <button class="btn btn-outline" onclick="clearFilters()" title="清除过滤">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>

                    <div class="control-divider"></div>

                    <div class="auto-refresh-control">
                        <label class="auto-refresh-label">
                            <input type="checkbox" id="autoRefreshToggle" onchange="toggleAutoRefresh()" checked>
                            <span>自动刷新</span>
                            <span class="refresh-interval">(5秒)</span>
                        </label>
                    </div>
                    <button class="btn btn-secondary" onclick="refreshLogs()">
                        <i class="fas fa-sync-alt"></i>
                        <span>刷新</span>
                    </button>
                </div>
            </div>

            <div class="panel-body">
                <div class="logs-content">
                    <div class="logs-container" id="logsContainer">
                        <div class="loading" id="loadingIndicator">
                            <i class="fas fa-spinner"></i>
                            <span>加载日志中...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 全局变量
    let autoRefreshInterval = null;
    let autoRefreshEnabled = true;
    let allLogs = []; // 存储所有日志数据
    let filteredLogs = []; // 存储过滤后的日志数据
    let availableTaskIds = new Set(); // 存储可用的任务ID

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
        loadLogs();
        startAutoRefresh();
    });

    // 页面卸载时清理自动刷新
    window.addEventListener('beforeunload', function() {
        stopAutoRefresh();
    });

    // 加载日志数据
    function loadLogs() {
        const container = document.getElementById('logsContainer');
        const loadingIndicator = document.getElementById('loadingIndicator');

        // 显示加载状态
        if (loadingIndicator) {
            loadingIndicator.style.display = 'flex';
        }

        // 构建查询参数
        const params = new URLSearchParams({
            pageSize: '500'
        });

        // 添加过滤参数（如果有的话）
        const levelFilter = document.getElementById('levelFilter');
        const taskIdFilter = document.getElementById('taskIdFilter');

        if (levelFilter && levelFilter.value) {
            params.append('level', levelFilter.value);
        }

        if (taskIdFilter && taskIdFilter.value) {
            params.append('taskId', taskIdFilter.value);
        }

        fetch(`/api/plugins/pcapPlay/logs?${params.toString()}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 存储所有日志数据（这里是过滤后的数据）
                    allLogs = data.data.logs || [];
                    filteredLogs = allLogs; // 后端已经过滤，前端直接使用

                    // 更新可用任务ID列表（基于所有数据，不是过滤后的）
                    updateAvailableTaskIds();

                    // 渲染日志
                    renderLogs(filteredLogs);
                } else {
                    showError('加载日志失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('加载日志失败:', error);
                showError('加载日志失败: ' + error.message);
            })
            .finally(() => {
                if (loadingIndicator) {
                    loadingIndicator.style.display = 'none';
                }
            });
    }

    // 渲染日志表格
    function renderLogs(logs) {
        const container = document.getElementById('logsContainer');

        if (!container) {
            console.error('日志容器元素未找到');
            return;
        }

        if (!logs || logs.length === 0) {
            const isEmpty = allLogs.length === 0;
            const emptyMessage = isEmpty ?
                '系统尚未生成运行日志' :
                '没有符合过滤条件的日志记录';
            const emptyTitle = isEmpty ? '暂无日志' : '无匹配结果';

            container.innerHTML = `
                <div class="empty-state">
                    <i class="fas fa-${isEmpty ? 'file-alt' : 'filter'}"></i>
                    <h3>${emptyTitle}</h3>
                    <p>${emptyMessage}</p>
                    ${!isEmpty ? '<button class="btn btn-secondary" onclick="clearFilters()">清除过滤条件</button>' : ''}
                </div>
            `;
            return;
        }

        const table = document.createElement('table');
        table.className = 'logs-table';

        // 创建表头
        table.innerHTML = `
            <thead>
                <tr>
                    <th class="col-time">时间</th>
                    <th class="col-level">级别</th>
                    <th class="col-taskid">任务ID</th>
                    <th class="col-message">消息</th>
                </tr>
            </thead>
            <tbody id="logsTableBody">
            </tbody>
        `;

        const tbody = table.querySelector('#logsTableBody');

        // 创建日志行
        logs.forEach(log => {
            const row = createLogRow(log);
            tbody.appendChild(row);
        });

        container.innerHTML = '';
        container.appendChild(table);
    }

    // 创建日志行
    function createLogRow(log) {
        const row = document.createElement('tr');

        // 格式化时间
        const time = formatTime(log.time);

        // 格式化级别
        const levelClass = getLevelClass(log.level);

        row.innerHTML = `
            <td class="col-time">
                <span class="log-time">${time}</span>
            </td>
            <td class="col-level">
                <span class="log-level ${levelClass}">${log.level}</span>
            </td>
            <td class="col-taskid">
                <span class="log-taskid">${log.source || ''}</span>
            </td>
            <td class="col-message">
                <span class="log-message">${escapeHtml(log.message)}</span>
            </td>
        `;

        return row;
    }

    // 格式化时间
    function formatTime(timeStr) {
        try {
            // 如果是ISO格式，转换为本地时间
            if (timeStr.includes('T')) {
                const date = new Date(timeStr);
                return date.toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                });
            }
            // 如果已经是格式化的时间字符串，直接返回
            return timeStr;
        } catch (error) {
            return timeStr;
        }
    }

    // 获取日志级别样式类
    function getLevelClass(level) {
        const levelLower = level.toLowerCase();
        switch (levelLower) {
            case 'error':
            case 'erro':
                return 'error';
            case 'warn':
            case 'warning':
                return 'warn';
            case 'debug':
                return 'debug';
            case 'info':
            default:
                return 'info';
        }
    }

    // HTML转义
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // 切换自动刷新
    function toggleAutoRefresh() {
        const toggle = document.getElementById('autoRefreshToggle');
        if (!toggle) {
            console.error('自动刷新切换按钮未找到');
            return;
        }

        autoRefreshEnabled = toggle.checked;

        if (autoRefreshEnabled) {
            startAutoRefresh();
        } else {
            stopAutoRefresh();
        }
    }

    // 开始自动刷新
    function startAutoRefresh() {
        if (autoRefreshInterval) {
            clearInterval(autoRefreshInterval);
        }

        if (autoRefreshEnabled) {
            autoRefreshInterval = setInterval(() => {
                loadLogs();
            }, 5000); // 5秒刷新一次
        }
    }

    // 停止自动刷新
    function stopAutoRefresh() {
        if (autoRefreshInterval) {
            clearInterval(autoRefreshInterval);
            autoRefreshInterval = null;
        }
    }

    // 手动刷新日志
    function refreshLogs() {
        loadLogs();
    }

    // 显示错误信息
    function showError(message) {
        const container = document.getElementById('logsContainer');
        if (!container) {
            console.error('日志容器元素未找到，无法显示错误信息:', message);
            return;
        }

        container.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-exclamation-triangle" style="color: #ef4444;"></i>
                <h3>加载失败</h3>
                <p>${escapeHtml(message)}</p>
            </div>
        `;
    }

    // 更新可用任务ID列表
    function updateAvailableTaskIds() {
        // 从后端获取所有可用任务ID
        fetch('/api/plugins/pcapPlay/logs?pageSize=1000') // 获取更多数据来收集任务ID
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    availableTaskIds.clear();

                    // 收集所有唯一的任务ID
                    (data.data.logs || []).forEach(log => {
                        if (log.source) {
                            availableTaskIds.add(log.source);
                        }
                    });

                    // 更新任务ID选择框
                    const taskIdFilter = document.getElementById('taskIdFilter');
                    const currentValue = taskIdFilter.value;

                    // 清空现有选项（保留"全部"选项）
                    taskIdFilter.innerHTML = '<option value="">全部</option>';

                    // 添加任务ID选项
                    Array.from(availableTaskIds).sort().forEach(taskId => {
                        const option = document.createElement('option');
                        option.value = taskId;
                        option.textContent = taskId;
                        taskIdFilter.appendChild(option);
                    });

                    // 恢复之前的选择
                    taskIdFilter.value = currentValue;
                }
            })
            .catch(error => {
                console.warn('获取任务ID失败:', error);
            });
    }

    // 应用过滤条件
    function applyFilters() {
        // 更新过滤控件样式
        updateFilterStyles();

        // 重新加载数据（后端过滤）
        loadLogs();
    }

    // 更新过滤控件样式
    function updateFilterStyles() {
        const levelFilter = document.getElementById('levelFilter');
        const taskIdFilter = document.getElementById('taskIdFilter');

        // 更新级别过滤器样式
        if (levelFilter) {
            if (levelFilter.value) {
                levelFilter.classList.add('filter-active');
            } else {
                levelFilter.classList.remove('filter-active');
            }
        }

        // 更新任务ID过滤器样式
        if (taskIdFilter) {
            if (taskIdFilter.value) {
                taskIdFilter.classList.add('filter-active');
            } else {
                taskIdFilter.classList.remove('filter-active');
            }
        }
    }

    // 清除过滤条件
    function clearFilters() {
        const levelFilter = document.getElementById('levelFilter');
        const taskIdFilter = document.getElementById('taskIdFilter');

        if (levelFilter) {
            levelFilter.value = '';
        }

        if (taskIdFilter) {
            taskIdFilter.value = '';
        }

        // 重新应用过滤（实际上是显示所有日志）
        applyFilters();
    }
</script>
{% endblock %}
