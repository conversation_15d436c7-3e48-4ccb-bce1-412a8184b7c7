# 网络模拟访问插件

## 功能描述

网络模拟访问插件支持从前台页面输入多行内容（IP地址、域名、协议配置等），后台通过Python的socket模拟客户端和服务器访问行为，使用tcpdump抓包生成对应的报文文件供用户下载。

## 主要功能

1. **多目标输入**：支持批量输入IP地址和域名
2. **协议配置**：支持HTTP、HTTPS、TCP、UDP等多种协议
3. **客户端模拟**：使用socket模拟真实的网络访问行为
4. **实时抓包**：使用tcpdump实时抓取网络报文
5. **文件下载**：生成pcap文件供用户下载分析

## 技术实现

- **后端**：Python Flask + socket + subprocess
- **前端**：HTML + CSS + JavaScript
- **抓包工具**：tcpdump
- **文件格式**：pcap

## 使用方法

1. 在输入框中输入目标IP或域名（每行一个）
2. 选择访问协议和端口
3. 配置抓包参数
4. 点击开始模拟按钮
5. 等待任务完成后下载抓包文件

## 目录结构

```
networkSimulator/
├── __init__.py          # 插件初始化
├── plugin.py            # 插件主类
├── network_simulator.py # 网络模拟核心功能
├── templates/           # HTML模板
├── static/             # 静态资源
├── uploads/            # 上传文件
├── reports/            # 生成的报告
└── logs/              # 日志文件
```
