#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文件上传功能修复效果
"""

import os
import sys
import tempfile
import logging
import time
import subprocess

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from plugins.networkSimulator.network_simulator import NetworkSimulator

def setupLogger():
    """设置日志"""
    logger = logging.getLogger('test_file_upload')
    logger.setLevel(logging.INFO)
    
    handler = logging.StreamHandler()
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    
    return logger

def createTestFile(filename: str, content: str) -> str:
    """创建测试文件"""
    with tempfile.NamedTemporaryFile(mode='w', suffix=f'_{filename}', delete=False) as f:
        f.write(content)
        return f.name

def testFileUpload():
    """测试文件上传功能"""
    logger = setupLogger()
    
    # 创建网络模拟器实例
    plugin_dir = os.path.dirname(os.path.abspath(__file__))
    simulator = NetworkSimulator(plugin_dir, logger)
    
    # 创建测试文件
    test_content = "这是一个测试文件内容，用于验证文件上传功能是否正常工作。\n" * 100
    test_file_path = createTestFile("test_upload.txt", test_content)
    
    try:
        # 准备文件信息
        files = [{
            'name': 'test_upload.txt',
            'path': test_file_path,
            'size': len(test_content.encode('utf-8'))
        }]
        
        # 创建输出目录
        output_dir = tempfile.mkdtemp(prefix='file_upload_test_')
        logger.info(f"输出目录: {output_dir}")
        
        # 执行文件传输模拟
        logger.info("开始执行文件传输模拟...")
        result = simulator.simulateFileTransfer(
            files=files,
            protocol='http',
            port=8080,
            output_dir=output_dir,
            target_ip="*************",
            target_domain="testfileserver.com"
        )
        
        # 输出结果
        logger.info("=" * 60)
        logger.info("文件传输模拟结果:")
        logger.info(f"成功状态: {result['success']}")
        logger.info(f"消息: {result['message']}")
        
        if 'details' in result:
            logger.info("详细信息:")
            for detail in result['details']:
                logger.info(f"  文件: {detail['file']}")
                logger.info(f"  成功: {detail['success']}")
                logger.info(f"  消息: {detail['message']}")
                if 'details' in detail:
                    logger.info(f"  详细: {detail['details']}")
        
        # 检查生成的pcap文件
        if 'pcap_files' in result:
            logger.info("生成的pcap文件:")
            for pcap_file in result['pcap_files']:
                if os.path.exists(pcap_file):
                    file_size = os.path.getsize(pcap_file)
                    logger.info(f"  {pcap_file} (大小: {file_size} 字节)")
                    
                    # 使用tcpdump分析pcap文件内容
                    try:
                        cmd = ['tcpdump', '-r', pcap_file, '-A', '-n']
                        process = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
                        if process.returncode == 0:
                            logger.info(f"  tcpdump分析结果:")
                            lines = process.stdout.split('\n')[:20]  # 只显示前20行
                            for line in lines:
                                if line.strip():
                                    logger.info(f"    {line}")
                        else:
                            logger.warning(f"  tcpdump分析失败: {process.stderr}")
                    except subprocess.TimeoutExpired:
                        logger.warning("  tcpdump分析超时")
                    except FileNotFoundError:
                        logger.warning("  tcpdump未安装，无法分析pcap文件")
                else:
                    logger.warning(f"  {pcap_file} 不存在")
        
        logger.info("=" * 60)
        
        return result['success']
        
    finally:
        # 清理测试文件
        try:
            os.unlink(test_file_path)
        except:
            pass

if __name__ == '__main__':
    success = testFileUpload()
    if success:
        print("测试通过：文件上传功能正常")
        sys.exit(0)
    else:
        print("测试失败：文件上传功能存在问题")
        sys.exit(1)
