#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的插件基类
只保留路由和视图注册功能
"""

from typing import Dict, Any, List
import logging


class BasePlugin:
    """简化的插件基类"""

    def __init__(self, **kwargs):
        self.app = kwargs.get('app', None)
        self._routes = []
        self._api_routes = []
        self._nav_items = []

        # 初始化基本属性
        self.name = kwargs.get('name', '')
        self.displayName = kwargs.get('displayName', self.name)
        self.description = kwargs.get('description', '')
        self.author = kwargs.get('author', '')
        self.version = kwargs.get('version', '1.0.0')

        # 初始化日志器
        self.logger = logging.getLogger(f"plugin.{self.name or 'unknown'}")

    def getName(self) -> str:
        """获取插件名称"""
        return self.name

    def getDisplayName(self) -> str:
        """获取插件显示名称"""
        return self.displayName

    def getDescription(self) -> str:
        """获取插件描述"""
        return self.description

    def getVersion(self) -> str:
        """获取插件版本"""
        return self.version

    def getAuthor(self) -> str:
        """获取插件作者"""
        return self.author

    def initPlugin(self) -> bool:
        """初始化插件"""
        try:
            self.logger.info(f"正在初始化插件: {self.getDisplayName()}")
            self._registerRoutes()
            self._registerApiRoutes()
            self._registerNavItems()
            self.onInitialize()
            self.logger.info(f"插件初始化成功: {self.getDisplayName()}")
            return True
        except Exception as e:
            self.logger.error(f"插件初始化失败: {self.getDisplayName()}, 错误: {str(e)}")
            return False

    def onInitialize(self):
        """插件初始化回调，子类可重写"""
        pass

    def _registerRoutes(self):
        """注册页面路由"""
        routes = self.getRoutes()
        for route in routes:
            self._routes.append(route)

    def _registerApiRoutes(self):
        """注册API路由"""
        apiRoutes = self.getApiRoutes()
        for route in apiRoutes:
            self._api_routes.append(route)

    def _registerNavItems(self):
        """注册导航项"""
        navItems = self.getNavItems()
        for item in navItems:
            self._nav_items.append(item)

    def getRoutes(self) -> List[Dict[str, Any]]:
        """获取页面路由配置，子类需要重写"""
        return []

    def getApiRoutes(self) -> List[Dict[str, Any]]:
        """获取API路由配置，子类需要重写"""
        return []

    def getNavItems(self) -> List[Dict[str, Any]]:
        """获取导航项配置，子类需要重写"""
        return []

    def getPluginRoutes(self) -> List[Dict[str, Any]]:
        """获取插件的所有路由"""
        return self._routes

    def getPluginApiRoutes(self) -> List[Dict[str, Any]]:
        """获取插件的所有API路由"""
        return self._api_routes

    def getPluginNavItems(self) -> List[Dict[str, Any]]:
        """获取插件的所有导航项"""
        return self._nav_items

    def getInfo(self) -> Dict[str, Any]:
        """获取插件基本信息"""
        return {
            'name': self.getName(),
            'displayName': self.getDisplayName(),
            'description': self.getDescription(),
            'version': self.getVersion(),
            'author': self.getAuthor()
        }


class PluginError(Exception):
    """插件异常"""
    pass
