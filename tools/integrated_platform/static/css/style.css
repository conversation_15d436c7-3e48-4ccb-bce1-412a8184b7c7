/* 集成工具平台 - 现代科技风格样式 */

/* CSS变量定义 */
:root {
    /* 主色调 - 科技蓝 */
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --primary-light: #3b82f6;

    /* 辅助色调 */
    --secondary-color: #64748b;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --danger-color: #ef4444;
    --info-color: #06b6d4;

    /* 中性色调 */
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;

    /* 背景色 */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #f1f5f9;

    /* 边框和阴影 */
    --border-color: #e2e8f0;
    --border-radius: 12px;
    --border-radius-sm: 8px;
    --border-radius-lg: 16px;

    /* 阴影 */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* 间距 */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;

    /* 字体 */
    --font-family: 'Inter', 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-mono: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;

    /* 侧边栏 */
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 80px;

    /* 动画 */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

/* 全局样式重置 */
* {
    box-sizing: border-box;
}

body {
    font-family: var(--font-family);
    background-color: var(--bg-secondary);
    color: var(--gray-800);
    margin: 0;
    padding: 0;
    line-height: 1.6;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* 科技布局容器 */
.tech-layout {
    display: flex;
    min-height: 100vh;
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--gray-100) 100%);
}

/* ==================== 侧边栏样式 ==================== */
.sidebar {
    width: var(--sidebar-width);
    background: linear-gradient(180deg, var(--gray-900) 0%, var(--gray-800) 100%);
    color: white;
    position: fixed;
    top: 0;
    left: 0;
    height: 100vh;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    transition: transform var(--transition-normal);
    box-shadow: var(--shadow-xl);
}

/* 侧边栏头部 */
.sidebar-header {
    padding: var(--spacing-xl) var(--spacing-lg);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.logo-container {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.logo-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    border-radius: var(--border-radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
    box-shadow: var(--shadow-md);
}

.logo-text h4 {
    font-weight: 700;
    font-size: 1.25rem;
    margin: 0;
    background: linear-gradient(135deg, #ffffff, #e2e8f0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.logo-text small {
    color: var(--gray-400);
    font-size: 0.75rem;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: var(--gray-400);
    font-size: 1.25rem;
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--border-radius-sm);
    transition: var(--transition-fast);
}

.sidebar-toggle:hover {
    color: white;
    background: rgba(255, 255, 255, 0.1);
}

/* 侧边栏菜单 */
.sidebar-menu {
    flex: 1;
    padding: var(--spacing-lg) 0;
    overflow-y: auto;
}

.menu-section {
    margin-bottom: var(--spacing-xl);
}

.menu-title {
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    color: var(--gray-400);
    padding: 0 var(--spacing-lg);
    margin-bottom: var(--spacing-md);
}

.nav-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-item {
    margin-bottom: var(--spacing-xs);
}

.nav-link {
    display: flex;
    align-items: center;
    padding: var(--spacing-md) var(--spacing-lg);
    color: var(--gray-300);
    text-decoration: none;
    transition: var(--transition-fast);
    border-radius: 0;
    position: relative;
}

.nav-link:hover {
    color: white;
    background: rgba(255, 255, 255, 0.05);
    text-decoration: none;
}

.nav-link.active {
    color: white;
    background: linear-gradient(90deg, var(--primary-color), transparent);
    border-right: 3px solid var(--primary-color);
}

.nav-link.active::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background: var(--primary-color);
    border-radius: 0 2px 2px 0;
}

.nav-icon {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: var(--spacing-md);
    font-size: 1rem;
}

.nav-text {
    font-weight: 500;
    font-size: 0.875rem;
}

.dropdown-arrow {
    margin-left: auto;
    font-size: 0.75rem;
    transition: var(--transition-fast);
}

.nav-link[aria-expanded="true"] .dropdown-arrow {
    transform: rotate(180deg);
}

/* 子菜单 */
.sub-nav-list {
    list-style: none;
    padding: 0;
    margin: 0;
    background: rgba(0, 0, 0, 0.2);
}

.sub-nav-item {
    margin: 0;
}

.sub-nav-link {
    display: flex;
    align-items: center;
    padding: var(--spacing-sm) var(--spacing-lg) var(--spacing-sm) calc(var(--spacing-lg) + 32px);
    color: var(--gray-400);
    text-decoration: none;
    font-size: 0.8125rem;
    transition: var(--transition-fast);
}

.sub-nav-link:hover {
    color: white;
    background: rgba(255, 255, 255, 0.05);
    text-decoration: none;
}

.sub-nav-link i {
    margin-right: var(--spacing-sm);
    font-size: 0.75rem;
}

/* 侧边栏底部 */
.sidebar-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    background: rgba(0, 0, 0, 0.2);
}

.status-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.status-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: 0.8125rem;
    color: var(--gray-400);
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--success-color);
    box-shadow: 0 0 8px rgba(16, 185, 129, 0.4);
}

.status-indicator.online {
    background: var(--success-color);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* 移动端导航切换 */
.mobile-nav-toggle {
    position: fixed;
    top: var(--spacing-lg);
    left: var(--spacing-lg);
    z-index: 1001;
    background: var(--gray-900);
    color: white;
    border: none;
    width: 48px;
    height: 48px;
    border-radius: var(--border-radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    box-shadow: var(--shadow-lg);
    transition: var(--transition-fast);
}

.mobile-nav-toggle:hover {
    background: var(--gray-800);
    transform: scale(1.05);
}

/* ==================== 主内容区域 ==================== */
.main-content {
    flex: 1;
    margin-left: var(--sidebar-width);
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    transition: margin-left var(--transition-normal);
}

/* 内容区域样式 */
.content-area {
    padding: var(--spacing-xl);
}

.system-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

.status-badge {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--border-radius);
    font-size: 0.8125rem;
    font-weight: 500;
    background: var(--gray-100);
    color: var(--gray-700);
}

.info-badge {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--border-radius);
    font-size: 0.8125rem;
    font-weight: 500;
    background: var(--gray-100);
    color: var(--gray-700);
}

.version-badge {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-xs) var(--spacing-sm);
    background: var(--primary-color);
    color: white;
    border-radius: var(--border-radius-sm);
    font-size: 0.75rem;
    font-weight: 500;
}

/* 内容区域 */
.content-area {
    flex: 1;
    padding: var(--spacing-xl);
    background: var(--bg-secondary);
}

/* ==================== 首页样式 ==================== */

/* 欢迎区域 */
.welcome-section {
    margin-bottom: var(--spacing-2xl);
}

.welcome-card {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-2xl);
    color: white;
    box-shadow: var(--shadow-xl);
    position: relative;
    overflow: hidden;
}

.welcome-card::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(50%, -50%);
}

.welcome-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
    position: relative;
    z-index: 1;
}

.welcome-icon {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.15);
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5rem;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.welcome-text h2 {
    font-size: 2.25rem;
    font-weight: 700;
    margin: 0 0 var(--spacing-sm) 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.welcome-text p {
    font-size: 1.125rem;
    margin: 0;
    opacity: 0.9;
}

.welcome-actions {
    display: flex;
    gap: var(--spacing-md);
    position: relative;
    z-index: 1;
}

.welcome-actions .btn {
    padding: var(--spacing-md) var(--spacing-xl);
    font-weight: 600;
    border-radius: var(--border-radius);
    transition: var(--transition-fast);
}

.welcome-actions .btn-primary {
    background: rgba(255, 255, 255, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: white;
    backdrop-filter: blur(10px);
}

.welcome-actions .btn-primary:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.welcome-actions .btn-outline-primary {
    background: transparent;
    border: 1px solid rgba(255, 255, 255, 0.5);
    color: white;
}

.welcome-actions .btn-outline-primary:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: white;
    transform: translateY(-2px);
}

/* 区域标题 */
.section-header {
    margin-bottom: var(--spacing-xl);
    text-align: center;
}

.section-title {
    font-size: 1.875rem;
    font-weight: 700;
    color: var(--gray-900);
    margin: 0 0 var(--spacing-sm) 0;
    background: linear-gradient(135deg, var(--gray-900), var(--gray-600));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-subtitle {
    font-size: 1rem;
    color: var(--gray-600);
    margin: 0;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

/* 仪表板区域 */
.dashboard-section {
    margin-bottom: var(--spacing-2xl);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

.stat-card {
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
    border: 1px solid var(--border-color);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
}

.stat-card.primary::before {
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
}

.stat-card.success::before {
    background: linear-gradient(90deg, var(--success-color), #34d399);
}

.stat-card.info::before {
    background: linear-gradient(90deg, var(--info-color), #22d3ee);
}

.stat-card.warning::before {
    background: linear-gradient(90deg, var(--warning-color), #fbbf24);
}

.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.75rem;
    margin-bottom: var(--spacing-lg);
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    color: white;
    box-shadow: var(--shadow-md);
}

.stat-card.success .stat-icon {
    background: linear-gradient(135deg, var(--success-color), #34d399);
}

.stat-card.info .stat-icon {
    background: linear-gradient(135deg, var(--info-color), #22d3ee);
}

.stat-card.warning .stat-icon {
    background: linear-gradient(135deg, var(--warning-color), #fbbf24);
}

.stat-content {
    margin-bottom: var(--spacing-lg);
}

.stat-number {
    font-size: 2.25rem;
    font-weight: 700;
    color: var(--gray-900);
    margin-bottom: var(--spacing-xs);
    line-height: 1;
}

.stat-label {
    font-size: 0.875rem;
    color: var(--gray-600);
    font-weight: 500;
    margin-bottom: var(--spacing-sm);
}

.stat-trend {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: 0.8125rem;
    color: var(--success-color);
    font-weight: 500;
}

.stat-action .btn {
    font-size: 0.8125rem;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--border-radius-sm);
}

/* 插件区域 */
.plugins-section {
    margin-bottom: var(--spacing-2xl);
}

.plugins-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: var(--spacing-xl);
}

.plugin-card {
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
    border: 1px solid var(--border-color);
    overflow: hidden;
}

.plugin-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.plugin-header {
    padding: var(--spacing-xl);
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-md);
}

.plugin-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
    flex-shrink: 0;
    box-shadow: var(--shadow-sm);
}

.plugin-info {
    flex: 1;
    min-width: 0;
}

.plugin-name {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-900);
    margin: 0 0 var(--spacing-xs) 0;
}

.plugin-meta {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    font-size: 0.8125rem;
}

.plugin-id {
    color: var(--gray-500);
    font-family: var(--font-mono);
    background: var(--gray-100);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--border-radius-sm);
}

.plugin-version {
    color: var(--primary-color);
    font-weight: 500;
}

.plugin-status {
    flex-shrink: 0;
}

.status-badge {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-md);
    border-radius: var(--border-radius);
    font-size: 0.8125rem;
    font-weight: 500;
}

.status-badge.active {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
    border: 1px solid rgba(16, 185, 129, 0.2);
}

.status-badge.active i {
    animation: pulse 2s infinite;
}

.plugin-body {
    padding: var(--spacing-xl);
}

.plugin-description {
    color: var(--gray-600);
    line-height: 1.6;
    margin-bottom: var(--spacing-lg);
}

.plugin-actions {
    margin-top: var(--spacing-lg);
}

.actions-label {
    font-size: 0.8125rem;
    font-weight: 500;
    color: var(--gray-700);
    margin-bottom: var(--spacing-sm);
}

.actions-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-sm);
}

.action-btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--gray-100);
    color: var(--gray-700);
    text-decoration: none;
    border-radius: var(--border-radius-sm);
    font-size: 0.8125rem;
    font-weight: 500;
    transition: var(--transition-fast);
    border: 1px solid var(--border-color);
}

.action-btn:hover {
    background: var(--primary-color);
    color: white;
    text-decoration: none;
    transform: translateY(-1px);
    box-shadow: var(--shadow-sm);
}

.plugin-footer {
    padding: var(--spacing-lg) var(--spacing-xl);
    background: var(--gray-50);
    border-top: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.plugin-author {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: 0.8125rem;
    color: var(--gray-600);
}

.plugin-controls {
    display: flex;
    gap: var(--spacing-xs);
}

.control-btn {
    width: 32px;
    height: 32px;
    background: none;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gray-500);
    cursor: pointer;
    transition: var(--transition-fast);
}

.control-btn:hover {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* 空状态 */
.empty-plugins-section {
    margin-bottom: var(--spacing-2xl);
}

.empty-state-card {
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-2xl);
    text-align: center;
    box-shadow: var(--shadow-md);
    border: 2px dashed var(--border-color);
}

.empty-icon {
    width: 80px;
    height: 80px;
    background: var(--gray-100);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: var(--gray-400);
    margin: 0 auto var(--spacing-lg);
}

.empty-content h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-sm);
}

.empty-content p {
    color: var(--gray-600);
    margin-bottom: var(--spacing-xl);
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

.empty-actions {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
}

/* 架构说明区域 */
.architecture-section {
    margin-bottom: var(--spacing-2xl);
}

.architecture-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);
}

.feature-card {
    background: var(--bg-primary);
    border-radius: var(--border-radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-md);
    transition: var(--transition-normal);
    border: 1px solid var(--border-color);
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--primary-light));
}

.feature-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

.feature-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--border-radius-lg);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-bottom: var(--spacing-lg);
    color: white;
    box-shadow: var(--shadow-md);
}

.feature-icon.primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
}

.feature-icon.success {
    background: linear-gradient(135deg, var(--success-color), #34d399);
}

.feature-icon.info {
    background: linear-gradient(135deg, var(--info-color), #22d3ee);
}

.feature-icon.warning {
    background: linear-gradient(135deg, var(--warning-color), #fbbf24);
}

.feature-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--gray-900);
    margin-bottom: var(--spacing-sm);
}

.feature-description {
    color: var(--gray-600);
    line-height: 1.6;
    margin-bottom: var(--spacing-md);
}

.feature-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.feature-list li {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-xs) 0;
    font-size: 0.875rem;
    color: var(--gray-600);
}

.feature-list li::before {
    content: '✓';
    width: 16px;
    height: 16px;
    background: var(--success-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
    font-weight: bold;
    flex-shrink: 0;
}

.architecture-actions {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md);
    flex-wrap: wrap;
}

/* ==================== 插件列表样式 ==================== */
.plugins-section {
    margin-bottom: 2rem;
}

.plugins-section .section-header {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, rgba(37, 99, 235, 0.1), rgba(59, 130, 246, 0.1));
    border-radius: 12px;
    border: 1px solid rgba(37, 99, 235, 0.2);
}

.plugins-section .section-title {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.plugins-section .section-subtitle {
    color: var(--text-secondary);
    margin-bottom: 0;
}

/* 插件表格样式 */
.table-responsive {
    border-radius: 12px;
    overflow: hidden;
    background: white;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.table {
    margin-bottom: 0;
}

.table thead th {
    background: var(--primary-color) !important;
    color: white;
    font-weight: 600;
    padding: 1rem;
    border: none;
}

.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    background-color: rgba(37, 99, 235, 0.05);
    transform: scale(1.005);
}

.plugin-name-cell .plugin-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.plugin-name-cell .fw-bold {
    font-size: 1.1rem;
}

.badge.bg-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color)) !important;
    padding: 0.5rem 0.8rem;
    font-weight: 500;
}

/* 空状态样式 */
.empty-state {
    background: rgba(255, 255, 255, 0.95);
    border: 2px dashed rgba(37, 99, 235, 0.3);
    border-radius: 12px;
    padding: 3rem;
    text-align: center;
}

.empty-state .empty-icon {
    opacity: 0.5;
}

.empty-state h4 {
    margin-top: 1rem;
    color: var(--text-secondary);
}

.empty-state p {
    margin-bottom: 0;
}

/* ==================== 按钮样式 ==================== */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-md) var(--spacing-lg);
    font-size: 0.875rem;
    font-weight: 500;
    line-height: 1;
    border-radius: var(--border-radius);
    border: 1px solid transparent;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition-fast);
    white-space: nowrap;
}

.btn:hover {
    text-decoration: none;
    transform: translateY(-1px);
}

.btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn-primary:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(37, 99, 235, 0.3);
}

.btn-primary:hover {
    background: var(--primary-dark);
    border-color: var(--primary-dark);
    color: white;
}

.btn-outline-primary {
    background: transparent;
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background: var(--primary-color);
    color: white;
}

.btn-outline-secondary {
    background: transparent;
    color: var(--gray-600);
    border-color: var(--border-color);
}

.btn-outline-secondary:hover {
    background: var(--gray-600);
    color: white;
    border-color: var(--gray-600);
}

.btn-outline-light {
    background: transparent;
    color: rgba(255, 255, 255, 0.8);
    border-color: rgba(255, 255, 255, 0.5);
}

.btn-outline-light:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border-color: white;
}

.btn-outline-dark {
    background: transparent;
    color: var(--gray-700);
    border-color: var(--gray-300);
}

.btn-outline-dark:hover {
    background: var(--gray-700);
    color: white;
    border-color: var(--gray-700);
}

.btn-sm {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: 0.8125rem;
}

.btn i {
    font-size: 0.875em;
}

/* 表格样式 */
.table {
    background-color: #fff;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
    background-color: #f8f9fa;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

/* 徽章样式 */
.badge {
    font-size: 0.75rem;
    font-weight: 500;
    padding: 0.375rem 0.75rem;
}

/* 模态框样式 */
.modal-content {
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.modal-header {
    border-bottom: 1px solid #e9ecef;
    background-color: #f8f9fa;
}

.modal-footer {
    border-top: 1px solid #e9ecef;
    background-color: #f8f9fa;
}

/* 表单样式 */
.form-control {
    border-radius: 6px;
    border: 1px solid #ced4da;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-label {
    font-weight: 500;
    color: #495057;
}

.form-text {
    font-size: 0.875rem;
    color: #6c757d;
}

/* 警告框样式 */
.alert {
    border: none;
    border-radius: 6px;
    font-weight: 500;
}

.alert-dismissible .btn-close {
    padding: 0.75rem 1rem;
}

/* 列表组样式 */
.list-group-item {
    border: none;
    border-bottom: 1px solid #e9ecef;
    padding: 1rem;
}

.list-group-item:last-child {
    border-bottom: none;
}

.list-group-item:hover {
    background-color: #f8f9fa;
}

/* 页脚样式 */
footer {
    margin-top: auto;
    border-top: 1px solid #e9ecef;
}

footer a {
    color: #6c757d;
    transition: color 0.2s ease;
}

footer a:hover {
    color: #007bff;
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 工具执行页面样式 */
.execution-result {
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 1rem;
    font-family: 'Courier New', monospace;
    white-space: pre-wrap;
    max-height: 400px;
    overflow-y: auto;
}

.execution-result.success {
    background-color: #d1edff;
    border-color: #bee5eb;
    color: #0c5460;
}

.execution-result.error {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

/* 日志页面样式 */
.log-entry {
    border-left: 4px solid #e9ecef;
    padding-left: 1rem;
    margin-bottom: 1rem;
}

.log-entry.success {
    border-left-color: #28a745;
}

.log-entry.error {
    border-left-color: #dc3545;
}

.log-entry.warning {
    border-left-color: #ffc107;
}

/* ==================== 响应式设计 ==================== */

/* 平板设备 */
@media (max-width: 1024px) {
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: var(--spacing-lg);
    }

    .plugins-grid {
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    }

    .architecture-grid {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }
}

/* 移动设备 */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
    }

    .sidebar.show {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }

    .mobile-nav-toggle {
        display: flex;
    }



    .page-title {
        font-size: 1.5rem;
    }

    .content-area {
        padding: var(--spacing-lg);
    }

    .welcome-content {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-lg);
    }

    .welcome-text h2 {
        font-size: 1.75rem;
    }

    .welcome-actions {
        justify-content: center;
        flex-wrap: wrap;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: var(--spacing-md);
    }

    .plugins-grid {
        grid-template-columns: 1fr;
    }

    .architecture-grid {
        grid-template-columns: 1fr;
    }

    .architecture-actions {
        flex-direction: column;
        align-items: center;
    }

    .plugin-header {
        flex-direction: column;
        text-align: center;
        gap: var(--spacing-md);
    }

    .plugin-meta {
        justify-content: center;
    }
}

/* 小屏幕设备 */
@media (max-width: 480px) {
    .welcome-card {
        padding: var(--spacing-xl);
    }

    .welcome-icon {
        width: 60px;
        height: 60px;
        font-size: 2rem;
    }

    .welcome-text h2 {
        font-size: 1.5rem;
    }

    .welcome-text p {
        font-size: 1rem;
    }

    .section-title {
        font-size: 1.5rem;
    }

    .stat-card,
    .plugin-card,
    .feature-card {
        padding: var(--spacing-lg);
    }

    .actions-buttons {
        flex-direction: column;
    }

    .action-btn {
        justify-content: center;
    }
}

/* 大屏幕优化 */
@media (min-width: 1200px) {
    .sidebar {
        width: 320px;
    }

    .main-content {
        margin-left: 320px;
    }

    .content-area {
        max-width: 1400px;
        margin: 0 auto;
        padding: var(--spacing-2xl);
    }

    .stats-grid {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* ==================== 工具样式 ==================== */

/* 警告框 */
.alert {
    border: none;
    border-radius: var(--border-radius);
    padding: var(--spacing-md) var(--spacing-lg);
    margin-bottom: var(--spacing-md);
    border-left: 4px solid;
}

.alert-primary {
    background: rgba(37, 99, 235, 0.1);
    color: var(--primary-dark);
    border-left-color: var(--primary-color);
}

.alert-success {
    background: rgba(16, 185, 129, 0.1);
    color: #065f46;
    border-left-color: var(--success-color);
}

.alert-warning {
    background: rgba(245, 158, 11, 0.1);
    color: #92400e;
    border-left-color: var(--warning-color);
}

.alert-danger {
    background: rgba(239, 68, 68, 0.1);
    color: #991b1b;
    border-left-color: var(--danger-color);
}

.alert-dismissible .btn-close {
    padding: var(--spacing-sm);
    margin: calc(var(--spacing-sm) * -1);
    margin-left: var(--spacing-md);
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid var(--gray-300);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--gray-100);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--gray-300);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gray-400);
}

/* 选择文本样式 */
::selection {
    background: rgba(37, 99, 235, 0.2);
    color: var(--gray-900);
}

/* 焦点样式 */
:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* 隐藏类 */
.d-none {
    display: none !important;
}

.d-lg-none {
    display: none !important;
}

@media (max-width: 991.98px) {
    .d-lg-none {
        display: block !important;
    }
}

/* 工具状态指示器 */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.status-indicator.active {
    background-color: #28a745;
}

.status-indicator.inactive {
    background-color: #6c757d;
}

/* 配置表单样式 */
.config-form {
    background-color: #fff;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 1.5rem;
}

.config-section {
    margin-bottom: 2rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #e9ecef;
}

.config-section:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.config-section h6 {
    color: #495057;
    font-weight: 600;
    margin-bottom: 1rem;
}

/* 工具图标 */
.tool-icon {
    width: 48px;
    height: 48px;
    background: linear-gradient(135deg, #007bff, #0056b3);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    margin-right: 1rem;
}

/* 搜索框样式 */
.search-box {
    position: relative;
}

.search-box .form-control {
    padding-right: 2.5rem;
}

.search-box .search-icon {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state h5 {
    margin-bottom: 0.5rem;
}

.empty-state p {
    margin-bottom: 1.5rem;
}
