{% extends "base.html" %}

{% block title %}{{ plugin_info.display_name }} - {{ app_name }}{% endblock %}

{% block extra_css %}
<link href="{{ url_for('static', filename='plugins/plugin_template/css/plugin.css') }}" rel="stylesheet">
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2>
                        <i class="fas fa-puzzle-piece me-2"></i>{{ plugin_info.display_name }}
                    </h2>
                    <p class="text-muted mb-0">{{ plugin_info.description }}</p>
                </div>
                <div>
                    <a href="/plugins/plugin_template/settings" class="btn btn-outline-primary">
                        <i class="fas fa-cog me-1"></i>设置
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- 状态卡片 -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ plugin_info.version }}</h4>
                            <p class="card-text">插件版本</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-code-branch fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ status }}</h4>
                            <p class="card-text">运行状态</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-heartbeat fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ items|length }}</h4>
                            <p class="card-text">项目数量</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-list fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-warning text-dark">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h4 class="card-title">{{ plugin_info.author }}</h4>
                            <p class="card-text">开发者</p>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-user fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 功能区域 -->
    <div class="row">
        <!-- 项目列表 -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>项目列表
                    </h5>
                </div>
                <div class="card-body">
                    {% if items %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>名称</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for item in items %}
                                <tr>
                                    <td>{{ item.id }}</td>
                                    <td>{{ item.name }}</td>
                                    <td>
                                        <span class="badge bg-{% if item.status == '完成' %}success{% elif item.status == '进行中' %}warning{% else %}secondary{% endif %}">
                                            {{ item.status }}
                                        </span>
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-outline-primary" onclick="processItem({{ item.id }})">
                                            <i class="fas fa-play me-1"></i>处理
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h6>暂无项目</h6>
                        <p class="text-muted">还没有任何项目数据</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- 配置信息 -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cog me-2"></i>当前配置
                    </h5>
                </div>
                <div class="card-body">
                    {% if config %}
                    <dl class="row">
                        {% for key, value in config.items() %}
                        <dt class="col-sm-6">{{ key }}</dt>
                        <dd class="col-sm-6">
                            {% if value is sameas true %}
                            <span class="badge bg-success">是</span>
                            {% elif value is sameas false %}
                            <span class="badge bg-secondary">否</span>
                            {% else %}
                            {{ value }}
                            {% endif %}
                        </dd>
                        {% endfor %}
                    </dl>
                    {% else %}
                    <p class="text-muted">使用默认配置</p>
                    {% endif %}
                    
                    <div class="mt-3">
                        <a href="/plugins/plugin_template/settings" class="btn btn-primary btn-sm">
                            <i class="fas fa-edit me-1"></i>修改配置
                        </a>
                    </div>
                </div>
            </div>

            <!-- API测试 -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-code me-2"></i>API测试
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-primary btn-sm" onclick="testAPI('info')">
                            <i class="fas fa-info-circle me-1"></i>获取信息
                        </button>
                        <button class="btn btn-outline-success btn-sm" onclick="testAPI('status')">
                            <i class="fas fa-heartbeat me-1"></i>检查状态
                        </button>
                        <button class="btn btn-outline-warning btn-sm" onclick="testAPI('process')">
                            <i class="fas fa-play me-1"></i>处理数据
                        </button>
                    </div>
                    
                    <div id="api-result" class="mt-3" style="display: none;">
                        <h6>API响应：</h6>
                        <pre class="bg-light p-2 rounded"><code id="api-response"></code></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='plugins/plugin_template/js/plugin.js') }}"></script>
<script>
// 处理项目
function processItem(itemId) {
    console.log('处理项目:', itemId);
    
    // 调用API处理项目
    testAPI('process', {item_id: itemId});
}

// 测试API
function testAPI(endpoint, data = null) {
    const apiUrl = `/api/plugins/plugin_template/${endpoint}`;
    const method = data ? 'POST' : 'GET';
    
    const options = {
        method: method,
        headers: {
            'Content-Type': 'application/json'
        }
    };
    
    if (data) {
        options.body = JSON.stringify(data);
    }
    
    fetch(apiUrl, options)
        .then(response => response.json())
        .then(result => {
            document.getElementById('api-result').style.display = 'block';
            document.getElementById('api-response').textContent = JSON.stringify(result, null, 2);
        })
        .catch(error => {
            console.error('API调用失败:', error);
            document.getElementById('api-result').style.display = 'block';
            document.getElementById('api-response').textContent = `错误: ${error.message}`;
        });
}
</script>
{% endblock %}
