/* 网络模拟访问插件样式 */

.network-simulator-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    width: 100%;
    box-sizing: border-box;
}

/* 强制确保所有子元素不超出容器宽度 */
.network-simulator-container * {
    max-width: 100%;
    box-sizing: border-box;
}

/* 确保页面头部宽度一致 */
.page-header {
    width: 100%;
    max-width: 100%;
}

.header-content {
    width: 100%;
    max-width: 100%;
}

/* 页面头部 */
.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    padding: 20px 30px;
    margin-bottom: 30px;
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.3);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.header-title {
    display: flex;
    align-items: center;
    gap: 15px;
}

.header-title i {
    font-size: 2.5rem;
    color: white;
}

.header-title h1 {
    margin: 0;
    color: white;
    font-size: 1.8rem;
    font-weight: 600;
}

.header-actions {
    display: flex;
    gap: 15px;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    text-decoration: none;
    border-radius: 8px;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    color: white;
    text-decoration: none;
}

.action-btn.active {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.4);
}

/* 内容面板 */
.content-panels {
    display: grid;
    gap: 25px;
    grid-template-columns: 1fr;
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
}

.panel {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    border: 1px solid #e8ecf4;
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
}

.panel-header {
    background: #f8fafc;
    padding: 20px 30px;
    border-bottom: 1px solid #e8ecf4;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.panel-header .panel-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
}

.panel-header .panel-icon i {
    font-size: 1.5rem;
    color: white;
}

.panel-title h3 {
    margin: 0 0 5px 0;
    color: #2d3748;
    font-size: 1.4rem;
    font-weight: 600;
}

.panel-title p {
    margin: 0;
    color: #718096;
    font-size: 0.95rem;
}

.panel-body {
    padding: 30px;
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
    overflow-x: auto; /* 如果内容过宽，允许水平滚动 */
}

/* 信息网格 */
.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.info-item {
    padding: 20px;
    background: #f8fafc;
    border-radius: 8px;
    border-left: 4px solid #667eea;
}

.info-label {
    font-size: 0.9rem;
    color: #718096;
    margin-bottom: 8px;
    font-weight: 500;
}

.info-value {
    font-size: 1.1rem;
    color: #2d3748;
    font-weight: 600;
}

/* 功能特性列表 */
.feature-list {
    display: grid;
    gap: 20px;
}

.feature-item {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    padding: 20px;
    background: #f8fafc;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.feature-item:hover {
    background: #edf2f7;
    transform: translateY(-2px);
}

.feature-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.feature-icon i {
    font-size: 1.3rem;
    color: white;
}

.feature-content h4 {
    margin: 0 0 8px 0;
    color: #2d3748;
    font-size: 1.2rem;
    font-weight: 600;
}

.feature-content p {
    margin: 0;
    color: #718096;
    line-height: 1.6;
}

/* 使用步骤 */
.usage-steps {
    display: grid;
    gap: 20px;
}

.step-item {
    display: flex;
    align-items: flex-start;
    gap: 20px;
    padding: 20px;
    background: #f8fafc;
    border-radius: 8px;
}

.step-number {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: 600;
    font-size: 1.1rem;
    flex-shrink: 0;
}

.step-content h4 {
    margin: 0 0 8px 0;
    color: #2d3748;
    font-size: 1.1rem;
    font-weight: 600;
}

.step-content p {
    margin: 0;
    color: #718096;
    line-height: 1.6;
}

/* 表单样式 */
.form-section {
    max-width: 900px;
}

.form-group {
    margin-bottom: 20px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.form-label {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    font-weight: 600;
    color: #2d3748;
}

.form-label i {
    color: #667eea;
}

.form-input,
.form-select,
.form-textarea {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-textarea {
    resize: vertical;
    min-height: 120px;
    font-family: 'Courier New', monospace;
}

.form-hint {
    margin-top: 6px;
    font-size: 0.9rem;
    color: #718096;
}

.form-actions {
    display: flex;
    gap: 15px;
    margin-top: 30px;
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 0.85rem;
    gap: 4px;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #e2e8f0;
    color: #4a5568;
}

.btn-secondary:hover {
    background: #cbd5e0;
    transform: translateY(-1px);
}

.btn-success {
    background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
    color: white;
}

.btn-success:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(72, 187, 120, 0.4);
}

.btn-danger {
    background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
    color: white;
}

.btn-danger:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(245, 101, 101, 0.4);
}

.btn-info {
    background: linear-gradient(135deg, #4299e1 0%, #3182ce 100%);
    color: white;
}

.btn-info:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(66, 153, 225, 0.4);
}

/* 状态面板 */
.status-content {
    margin-bottom: 25px;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #e2e8f0;
}

.status-item:last-child {
    border-bottom: none;
}

.status-label {
    font-weight: 600;
    color: #4a5568;
}

.status-value {
    color: #2d3748;
}

.status-badge {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
}

.status-pending {
    background: #fed7d7;
    color: #c53030;
}

.status-running {
    background: #bee3f8;
    color: #2b6cb0;
}

.status-completed {
    background: #c6f6d5;
    color: #2f855a;
}

.status-failed {
    background: #fed7d7;
    color: #c53030;
}

/* 进度条 */
.progress-section {
    margin: 25px 0;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e2e8f0;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 10px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    width: 0%;
    transition: width 0.3s ease;
}

.progress-text {
    text-align: center;
    color: #718096;
    font-size: 0.9rem;
}

.action-buttons {
    display: flex;
    gap: 15px;
    margin-top: 25px;
}

/* 任务列表样式 */
.loading-state,
.empty-state {
    text-align: center;
    padding: 60px 20px;
    width: 100%;
    max-width: 100%;
    min-width: 850px; /* 与任务列表保持相同的最小宽度 */
    box-sizing: border-box;
    background: white;
    border-radius: 8px;
    overflow: hidden;
}

.loading-spinner i {
    font-size: 3rem;
    color: #667eea;
    margin-bottom: 20px;
}

.loading-text {
    color: #718096;
    font-size: 1.1rem;
}

.empty-icon i {
    font-size: 4rem;
    color: #cbd5e0;
    margin-bottom: 20px;
}

.empty-text h4 {
    margin: 0 0 10px 0;
    color: #4a5568;
    font-size: 1.3rem;
}

.empty-text p {
    margin: 0 0 25px 0;
    color: #718096;
}

.task-list {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    width: 100%;
    max-width: 100%;
}

.task-header {
    display: grid;
    grid-template-columns: 80px 280px 1fr 100px 150px 1fr 120px;
    gap: 20px;
    padding: 15px 20px;
    background: #f8fafc;
    border-bottom: 2px solid #e2e8f0;
    font-weight: 600;
    color: #4a5568;
    font-size: 0.9rem;
    min-width: 0; /* 防止网格项溢出 */
    width: 100%;
    max-width: 100%;
}

.task-body {
    max-height: 600px;
    overflow-y: auto;
    width: 100%;
    max-width: 100%;
}

.task-row {
    display: grid;
    grid-template-columns: 80px 280px 1fr 100px 150px 1fr 120px;
    gap: 20px;
    padding: 15px 20px;
    border-bottom: 1px solid #e2e8f0;
    align-items: center;
    transition: background 0.2s ease;
    min-width: 0; /* 防止网格项溢出 */
    width: 100%;
    max-width: 100%;
}

.task-row:hover {
    background: #f8fafc;
}

.task-col {
    font-size: 0.9rem;
    min-width: 0; /* 防止内容溢出 */
    overflow: hidden;
    text-overflow: ellipsis;
}

.task-id {
    font-family: 'Courier New', monospace;
    color: #667eea;
    font-weight: 600;
}

.task-name {
    font-weight: 600;
    color: #2d3748;
}

.task-time {
    color: #718096;
    font-size: 0.85rem;
}

.task-actions {
    display: flex;
    gap: 8px;
}

.action-icon {
    width: 32px;
    height: 32px;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
    background: #e2e8f0;
    color: #4a5568;
}

.action-icon:hover {
    transform: translateY(-1px);
}

.action-icon.view {
    background: #bee3f8;
    color: #2b6cb0;
}

.action-icon.download {
    background: #c6f6d5;
    color: #2f855a;
}

.action-icon.delete {
    background: #fed7d7;
    color: #c53030;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    align-items: center;
    justify-content: center;
}

.modal.show {
    display: flex;
}

.modal-content {
    background: white;
    border-radius: 12px;
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
}

.modal-small {
    max-width: 400px;
}

.modal-header {
    padding: 20px 30px;
    border-bottom: 1px solid #e2e8f0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    color: #2d3748;
    font-size: 1.3rem;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: #718096;
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background: #e2e8f0;
    color: #4a5568;
}

.modal-body {
    padding: 30px;
}

.modal-footer {
    padding: 20px 30px;
    border-top: 1px solid #e2e8f0;
    display: flex;
    gap: 15px;
    justify-content: flex-end;
}

.detail-section h4 {
    margin: 0 0 20px 0;
    color: #2d3748;
    font-size: 1.1rem;
    font-weight: 600;
    border-bottom: 2px solid #667eea;
    padding-bottom: 8px;
}

.detail-grid {
    display: grid;
    gap: 15px;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #e2e8f0;
}

.detail-item:last-child {
    border-bottom: none;
}

.detail-label {
    font-weight: 600;
    color: #4a5568;
}

.detail-value {
    color: #2d3748;
    text-align: right;
}

.confirm-content {
    display: flex;
    align-items: center;
    gap: 20px;
}

.confirm-icon {
    width: 60px;
    height: 60px;
    background: #fed7d7;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.confirm-icon i {
    font-size: 1.5rem;
    color: #c53030;
}

.confirm-text p {
    margin: 0 0 8px 0;
    color: #2d3748;
}

.confirm-text .text-muted {
    color: #718096;
    font-size: 0.9rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .network-simulator-container {
        padding: 15px;
    }

    .header-content {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .header-title h1 {
        font-size: 1.5rem;
    }

    .header-actions {
        flex-wrap: wrap;
        justify-content: center;
        gap: 10px;
    }

    .action-btn {
        padding: 10px 16px;
        font-size: 0.9rem;
    }

    .form-section {
        max-width: 100%;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .task-header,
    .task-row {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .task-col {
        padding: 5px 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .modal-content {
        width: 95%;
        margin: 20px;
    }

    .panel-body {
        padding: 20px;
    }

    .task-list {
        overflow-x: auto;
    }

    .task-header,
    .task-row {
        min-width: 600px; /* 确保最小宽度 */
    }
}

/* 任务类型选择样式 */
.task-type-options {
    display: flex;
    gap: 20px;
    margin-top: 10px;
}

.task-type-option {
    flex: 1;
}

.task-type-option input[type="radio"] {
    display: none;
}

.task-type-label {
    display: block;
    padding: 20px;
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    background: #f8fafc;
    min-height: 120px; /* 确保两个选项高度一致 */
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.task-type-label:hover {
    border-color: #3b82f6;
    background: #eff6ff;
}

.task-type-option input[type="radio"]:checked + .task-type-label {
    border-color: #3b82f6;
    background: #dbeafe;
    color: #1e40af;
}

.task-type-label i {
    font-size: 2rem;
    margin-bottom: 10px;
    display: block;
    color: #6b7280;
}

.task-type-option input[type="radio"]:checked + .task-type-label i {
    color: #3b82f6;
}

.task-type-label span {
    font-weight: 600;
    font-size: 1.1rem;
    display: block;
    margin-bottom: 5px;
}

.task-type-label small {
    color: #6b7280;
    font-size: 0.9rem;
}

/* 文件上传样式 */
.file-upload-area {
    margin-top: 10px;
}

.file-drop-zone {
    border: 2px dashed #cbd5e0;
    border-radius: 12px;
    padding: 40px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #f7fafc;
}

.file-drop-zone:hover {
    border-color: #3b82f6;
    background: #eff6ff;
}

.file-drop-zone i {
    font-size: 3rem;
    color: #a0aec0;
    margin-bottom: 15px;
}

.file-drop-zone p {
    margin: 0 0 10px 0;
    font-size: 1.1rem;
    color: #4a5568;
    font-weight: 500;
}

.file-drop-zone small {
    color: #718096;
    font-size: 0.9rem;
}

.file-list {
    margin-top: 20px;
}

.file-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    background: #f7fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    margin-bottom: 8px;
}

.file-info {
    display: flex;
    align-items: center;
    gap: 12px;
}

.file-icon {
    width: 32px;
    height: 32px;
    background: #3b82f6;
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.9rem;
}

.file-details h4 {
    margin: 0 0 4px 0;
    font-size: 0.95rem;
    color: #2d3748;
    font-weight: 500;
}

.file-details p {
    margin: 0;
    font-size: 0.85rem;
    color: #718096;
}

.file-remove {
    background: #fed7d7;
    border: none;
    color: #c53030;
    width: 28px;
    height: 28px;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.file-remove:hover {
    background: #feb2b2;
}

/* ==================== 批量操作样式 ==================== */

/* 批量操作按钮组 */
.batch-actions {
    display: flex;
    gap: 10px;
    align-items: center;
    margin-right: 15px;
}

.batch-actions .btn {
    padding: 8px 16px;
    font-size: 0.9rem;
    border-radius: 6px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 6px;
}

.batch-actions .btn-danger {
    background: #e53e3e;
    color: white;
}

.batch-actions .btn-danger:hover {
    background: #c53030;
    transform: translateY(-1px);
}

.batch-actions .btn-secondary {
    background: #718096;
    color: white;
}

.batch-actions .btn-secondary:hover {
    background: #4a5568;
    transform: translateY(-1px);
}

/* 任务选择列样式 */
.task-select {
    display: flex;
    align-items: center;
    gap: 8px;
    justify-content: center;
}

.task-select input[type="checkbox"] {
    width: 16px;
    height: 16px;
    cursor: pointer;
    accent-color: #667eea;
}

.task-select label {
    font-size: 0.85rem;
    color: #4a5568;
    cursor: pointer;
    margin: 0;
    user-select: none;
}

/* 任务复选框样式 */
.task-checkbox {
    width: 16px;
    height: 16px;
    cursor: pointer;
    accent-color: #667eea;
}

/* 加载状态样式 */
.loading-text {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #4a5568;
    font-size: 0.9rem;
}

.loading-text i {
    color: #667eea;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .batch-actions {
        flex-direction: column;
        gap: 8px;
        margin-right: 0;
        margin-bottom: 10px;
    }

    .batch-actions .btn {
        width: 100%;
        justify-content: center;
    }

    .task-header,
    .task-row {
        grid-template-columns: 60px 1fr;
        gap: 10px;
    }

    .task-select {
        justify-content: flex-start;
    }

    .task-select label {
        display: none; /* 在小屏幕上隐藏"全选"文字 */
    }
}
