# 报文修改工具 (pcapChange)

## 功能概述

pcapChange是一个用于批量修改网络报文IP地址的工具插件。它可以：

- 批量上传多个PCAP文件
- 修改报文的源IP和目标IP地址
- 支持IP地址自动递增
- 将处理后的文件打包为tar.gz压缩包
- 提供下载功能

## 使用流程

### 1. 文件上传
- 支持拖拽上传或点击选择文件
- 支持的文件格式：`.pcap`, `.cap`, `.pcapng`
- 可以同时上传多个文件进行批量处理

### 2. 参数配置
- **源IP地址 (Source IP)**：设置新的源IP地址
- **目标IP地址 (Destination IP)**：设置新的目标IP地址
- 多个文件时IP地址会自动递增（如：*********** → *********** → ***********）

### 3. 文件处理
- 系统会调用tcpprep和tcprewrite工具进行报文修改
- 处理过程中会显示进度信息
- 处理完成后自动创建tar.gz压缩包

### 4. 下载结果
- 处理完成后提供下载链接
- 下载的文件为tar.gz格式，包含所有处理后的PCAP文件

## 技术实现

### 依赖工具
- **tcpprep**：用于分析PCAP文件，生成cache文件
- **tcprewrite**：用于重写报文的IP地址和MAC地址

### 处理流程
1. 上传文件到临时目录
2. 对每个文件执行tcpprep生成cache文件
3. 使用tcprewrite修改IP地址
4. 将处理后的文件打包为tar.gz
5. 清理临时文件
6. 提供下载链接

### IP地址递增算法
- 支持正确的IP地址自增长
- 避免生成非法IP地址
- 支持跨网段递增

## API接口

### 文件上传
```
POST /plugins/pcapChange/upload
Content-Type: multipart/form-data

参数：
- files: 上传的PCAP文件列表

返回：
{
  "success": true,
  "data": {
    "taskId": "任务ID",
    "files": [文件信息列表]
  }
}
```

### 处理文件
```
POST /plugins/pcapChange/process
Content-Type: application/json

参数：
{
  "taskId": "任务ID",
  "srcIp": "源IP地址",
  "dstIp": "目标IP地址"
}

返回：
{
  "success": true,
  "data": {
    "taskId": "任务ID",
    "processedCount": 处理文件数量,
    "downloadUrl": "下载链接"
  }
}
```

### 下载结果
```
GET /plugins/pcapChange/download/{taskId}

返回：tar.gz压缩包文件
```

### 查询状态
```
GET /plugins/pcapChange/status/{taskId}

返回：
{
  "success": true,
  "data": {
    "taskId": "任务ID",
    "uploaded": true,
    "processed": true,
    "downloadReady": true,
    "fileCount": 文件数量,
    "fileSize": 压缩包大小
  }
}
```

## 配置文件

### frag.conf
位置：`uploads/frag.conf`

用于配置tcprewrite的分片参数：
```
fragment_size=1500
fragment_enable=1
fragment_overlap=0
fragment_timeout=30
```

## 目录结构

```
pcapChange/
├── plugin.py              # 插件主文件
├── pcapChange.py          # 核心处理逻辑
├── templates/             # 模板文件
│   └── pcapChange/
│       └── index.html     # 前端页面
├── uploads/               # 上传目录
│   └── frag.conf         # 分片配置文件
├── output/                # 输出目录
└── README.md             # 说明文档
```

## 注意事项

1. **系统依赖**：需要安装tcpreplay工具包
   ```bash
   sudo apt-get install tcpreplay
   ```

2. **文件大小限制**：建议单个文件不超过100MB

3. **临时文件清理**：系统会自动清理处理过程中的临时文件

4. **安全性**：上传的文件会存储在服务器临时目录中，建议定期清理

5. **并发处理**：每个任务使用唯一的taskId，支持多用户并发使用

## 错误处理

- 文件格式验证
- IP地址格式验证
- 工具执行错误处理
- 文件系统错误处理
- 网络传输错误处理

## 版本信息

- 版本：1.0.0
- 作者：LiaoJunBO
- 更新日期：2025-07-03
