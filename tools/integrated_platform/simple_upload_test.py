#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的文件上传测试，验证文件内容是否被正确传输
"""

import requests
import tempfile
import threading
import time
import json
from http.server import HTTPServer, BaseHTTPRequestHandler

class TestUploadHandler(BaseHTTPRequestHandler):
    """测试用的文件上传处理器"""
    
    def do_POST(self):
        """处理POST请求"""
        content_length = int(self.headers.get('Content-Length', 0))
        content_type = self.headers.get('Content-Type', '')
        
        print(f"接收到POST请求:")
        print(f"  Content-Length: {content_length}")
        print(f"  Content-Type: {content_type}")
        
        # 读取POST数据
        post_data = self.rfile.read(content_length)
        print(f"  实际接收数据大小: {len(post_data)} 字节")
        
        # 解析multipart数据
        if 'multipart/form-data' in content_type:
            boundary_match = content_type.split('boundary=')
            if len(boundary_match) > 1:
                boundary = boundary_match[1].strip()
                print(f"  Boundary: {boundary}")
                
                parts = post_data.split(f'--{boundary}'.encode())
                print(f"  分割后的部分数量: {len(parts)}")
                
                for i, part in enumerate(parts):
                    if b'Content-Disposition' in part and b'filename=' in part:
                        print(f"  找到文件部分 {i}:")
                        
                        # 查找文件内容
                        content_start = part.find(b'\r\n\r\n')
                        if content_start != -1:
                            file_content = part[content_start + 4:]
                            if file_content.endswith(b'\r\n'):
                                file_content = file_content[:-2]
                            
                            print(f"    文件内容大小: {len(file_content)} 字节")
                            print(f"    文件内容前100字节: {file_content[:100]}")
                            
                            # 检查是否包含我们的测试内容
                            if b'FILE_UPLOAD_TEST_CONTENT' in file_content:
                                print("    ✓ 找到测试文件标识内容！")
                            else:
                                print("    ✗ 未找到测试文件标识内容")
        
        # 发送响应
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        
        response = {
            'status': 'success',
            'received_size': len(post_data),
            'content_type': content_type
        }
        
        self.wfile.write(json.dumps(response).encode('utf-8'))
    
    def log_message(self, format, *args):
        """禁用默认日志"""
        pass

def startTestServer():
    """启动测试服务器"""
    server = HTTPServer(('127.0.0.1', 9999), TestUploadHandler)
    server_thread = threading.Thread(target=server.serve_forever, daemon=True)
    server_thread.start()
    return server

def testFileUpload():
    """测试文件上传"""
    print("启动测试服务器...")
    server = startTestServer()
    time.sleep(1)  # 等待服务器启动
    
    # 创建测试文件
    test_content = "FILE_UPLOAD_TEST_CONTENT: 这是测试文件内容\n"
    test_content += "包含中文和英文字符\n"
    test_content += "=" * 50 + "\n"
    test_content += "Additional test content line\n" * 10
    
    with tempfile.NamedTemporaryFile(mode='w', suffix='_test.txt', delete=False) as f:
        f.write(test_content)
        test_file_path = f.name
    
    print(f"创建测试文件: {test_file_path}")
    print(f"文件大小: {len(test_content.encode('utf-8'))} 字节")
    
    try:
        # 读取文件内容
        with open(test_file_path, 'rb') as f:
            file_content = f.read()
        
        print(f"读取文件内容大小: {len(file_content)} 字节")
        
        # 发送文件上传请求
        files = {'file': ('test.txt', file_content)}
        data = {'filename': 'test.txt', 'size': len(file_content)}
        
        print("\n发送文件上传请求...")
        response = requests.post(
            'http://127.0.0.1:9999/upload',
            files=files,
            data=data,
            timeout=10
        )
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        return response.status_code == 200
        
    finally:
        # 清理
        import os
        try:
            os.unlink(test_file_path)
        except:
            pass
        server.shutdown()

if __name__ == '__main__':
    print("=" * 60)
    print("简单文件上传测试")
    print("=" * 60)
    
    success = testFileUpload()
    
    print("=" * 60)
    if success:
        print("✓ 测试通过：文件内容正确传输")
    else:
        print("✗ 测试失败：文件内容传输有问题")
    print("=" * 60)
