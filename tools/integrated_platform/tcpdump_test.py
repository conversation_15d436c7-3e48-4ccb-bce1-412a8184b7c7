#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用tcpdump验证文件上传的网络流量
"""

import requests
import tempfile
import threading
import time
import json
import subprocess
import os
import signal
from http.server import HTTPServer, BaseHTTPRequestHandler

class TestUploadHandler(BaseHTTPRequestHandler):
    """测试用的文件上传处理器"""
    
    def do_POST(self):
        """处理POST请求"""
        content_length = int(self.headers.get('Content-Length', 0))
        post_data = self.rfile.read(content_length)
        
        # 发送响应
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        
        response = {'status': 'success', 'received_size': len(post_data)}
        self.wfile.write(json.dumps(response).encode('utf-8'))
    
    def log_message(self, format, *args):
        """禁用默认日志"""
        pass

def startTcpdump(output_file, port):
    """启动tcpdump抓包"""
    cmd = [
        'tcpdump',
        '-i', 'lo',
        '-w', output_file,
        '-s', '0',
        'port', str(port)
    ]
    
    print(f"启动tcpdump: {' '.join(cmd)}")
    
    try:
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            preexec_fn=os.setsid
        )
        return process
    except Exception as e:
        print(f"启动tcpdump失败: {e}")
        return None

def stopTcpdump(process):
    """停止tcpdump"""
    if process:
        try:
            os.killpg(os.getpgid(process.pid), signal.SIGTERM)
            process.wait(timeout=5)
        except:
            try:
                os.killpg(os.getpgid(process.pid), signal.SIGKILL)
                process.wait()
            except:
                pass

def analyzePcap(pcap_file):
    """分析pcap文件"""
    if not os.path.exists(pcap_file):
        print(f"pcap文件不存在: {pcap_file}")
        return False
    
    file_size = os.path.getsize(pcap_file)
    print(f"pcap文件大小: {file_size} 字节")
    
    if file_size <= 24:  # 只有pcap头部
        print("pcap文件只包含头部，没有数据包")
        return False
    
    try:
        # 使用tcpdump分析
        cmd = ['tcpdump', '-r', pcap_file, '-A', '-n']
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            output = result.stdout
            print("tcpdump分析结果:")
            print("-" * 40)
            
            # 检查是否包含我们的测试内容
            if 'FILE_UPLOAD_TEST_CONTENT' in output:
                print("✓ 在网络流量中找到文件内容！")
                return True
            else:
                print("✗ 在网络流量中未找到文件内容")
                
            # 显示部分输出
            lines = output.split('\n')[:30]
            for line in lines:
                if line.strip():
                    print(line)
            
            return 'POST' in output and 'multipart' in output
        else:
            print(f"tcpdump分析失败: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"分析pcap文件失败: {e}")
        return False

def testWithTcpdump():
    """使用tcpdump测试文件上传"""
    port = 9998
    pcap_file = '/tmp/upload_test.pcap'
    
    # 启动tcpdump
    tcpdump_process = startTcpdump(pcap_file, port)
    if not tcpdump_process:
        print("无法启动tcpdump，可能需要root权限")
        return False
    
    time.sleep(2)  # 等待tcpdump启动
    
    try:
        # 启动测试服务器
        server = HTTPServer(('127.0.0.1', port), TestUploadHandler)
        server_thread = threading.Thread(target=server.serve_forever, daemon=True)
        server_thread.start()
        time.sleep(1)
        
        # 创建测试文件
        test_content = "FILE_UPLOAD_TEST_CONTENT: 这是用于tcpdump测试的文件内容\n"
        test_content += "包含中文和英文字符用于验证\n"
        test_content += "=" * 50 + "\n"
        test_content += "Test line for network capture verification\n" * 5
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='_tcpdump_test.txt', delete=False) as f:
            f.write(test_content)
            test_file_path = f.name
        
        print(f"创建测试文件，大小: {len(test_content.encode('utf-8'))} 字节")
        
        # 读取文件内容并发送
        with open(test_file_path, 'rb') as f:
            file_content = f.read()
        
        files = {'file': ('tcpdump_test.txt', file_content)}
        data = {'filename': 'tcpdump_test.txt'}
        
        print("发送文件上传请求...")
        response = requests.post(
            f'http://127.0.0.1:{port}/upload',
            files=files,
            data=data,
            timeout=10
        )
        
        print(f"响应状态码: {response.status_code}")
        
        # 等待一下确保数据包被捕获
        time.sleep(2)
        
        # 停止tcpdump
        stopTcpdump(tcpdump_process)
        time.sleep(1)
        
        # 分析pcap文件
        print("\n分析抓包结果:")
        success = analyzePcap(pcap_file)
        
        # 清理
        try:
            os.unlink(test_file_path)
            server.shutdown()
        except:
            pass
        
        return success
        
    except Exception as e:
        print(f"测试过程中出错: {e}")
        stopTcpdump(tcpdump_process)
        return False

if __name__ == '__main__':
    print("=" * 60)
    print("使用tcpdump验证文件上传网络流量")
    print("=" * 60)
    
    success = testWithTcpdump()
    
    print("=" * 60)
    if success:
        print("✓ 测试通过：网络流量中包含文件内容")
    else:
        print("✗ 测试失败：网络流量中未找到文件内容")
    print("=" * 60)
