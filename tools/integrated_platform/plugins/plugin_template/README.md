# 插件开发模板

这是一个完整的插件开发模板，展示了如何为集成工具平台开发标准插件。

## 目录结构

```
plugin_template/
├── __init__.py              # 包初始化文件
├── plugin.py                # 插件主文件
├── README.md               # 插件文档
├── templates/              # 模板文件目录
│   └── plugin_template/
│       ├── index.html      # 主页面模板
│       └── settings.html   # 设置页面模板
└── static/                 # 静态资源目录
    ├── css/
    │   └── plugin.css      # 插件样式
    └── js/
        └── plugin.js       # 插件脚本
```

## 核心组件

### 1. 插件主类 (plugin.py)

插件主类必须继承 `BasePlugin` 并实现以下方法：

#### 必需方法
- `get_name()`: 返回插件唯一标识符
- `get_display_name()`: 返回插件显示名称
- `get_description()`: 返回插件描述
- `get_version()`: 返回插件版本

#### 可选方法
- `get_author()`: 返回插件作者
- `get_dependencies()`: 返回插件依赖列表
- `get_config_schema()`: 返回配置模式定义
- `get_default_config()`: 返回默认配置
- `get_routes()`: 返回页面路由配置
- `get_api_routes()`: 返回API路由配置
- `get_nav_items()`: 返回导航项配置
- `on_initialize()`: 插件初始化回调
- `on_cleanup()`: 插件清理回调

### 2. 路由配置

#### 页面路由
```python
def get_routes(self) -> List[Dict[str, Any]]:
    return [
        {
            "rule": "/",                    # 路由规则
            "view_func": self.index_page,   # 视图函数
            "methods": ["GET"],             # HTTP方法
            "endpoint": "index"             # 端点名称
        }
    ]
```

#### API路由
```python
def get_api_routes(self) -> List[Dict[str, Any]]:
    return [
        {
            "rule": "/info",
            "view_func": self.api_get_info,
            "methods": ["GET"],
            "endpoint": "info"
        }
    ]
```

### 3. 导航配置

```python
def get_nav_items(self) -> List[Dict[str, Any]]:
    return [
        {
            "title": "插件名称",
            "url": "/plugins/plugin_name/",
            "icon": "fas fa-puzzle-piece",
            "order": 100
        }
    ]
```

### 4. 配置管理

#### 配置模式定义
```python
def get_config_schema(self) -> Dict[str, Any]:
    return {
        "enable_feature": {
            "type": "boolean",
            "required": False,
            "description": "启用功能",
            "default": True
        },
        "max_items": {
            "type": "integer",
            "required": False,
            "description": "最大项目数",
            "default": 10,
            "min": 1,
            "max": 100
        }
    }
```

## 开发步骤

### 1. 创建插件目录
```bash
mkdir plugins/my_plugin
cd plugins/my_plugin
```

### 2. 复制模板文件
```bash
cp -r ../plugin_template/* .
```

### 3. 修改插件信息
编辑 `plugin.py` 文件，修改以下方法：
- `get_name()`: 修改为您的插件名称
- `get_display_name()`: 修改为显示名称
- `get_description()`: 修改为插件描述
- `get_version()`: 设置版本号
- `get_author()`: 设置作者信息

### 4. 实现功能逻辑
- 添加页面路由处理函数
- 添加API接口处理函数
- 实现业务逻辑

### 5. 创建前端页面
- 在 `templates/` 目录下创建模板文件
- 在 `static/` 目录下添加CSS和JS文件

### 6. 测试插件
- 重启应用或重新加载插件
- 访问插件页面测试功能
- 调用API接口验证功能

## 最佳实践

### 1. 命名规范
- 插件名称使用小写字母和下划线
- 类名使用驼峰命名法
- 函数名使用小写字母和下划线

### 2. 错误处理
- 在所有视图函数中添加异常处理
- 使用插件日志记录器记录错误
- 返回标准的错误响应格式

### 3. 配置管理
- 定义完整的配置模式
- 提供合理的默认值
- 实现配置验证逻辑

### 4. 前端集成
- 使用平台提供的基础模板
- 遵循平台的UI设计规范
- 确保响应式设计

### 5. API设计
- 遵循RESTful设计原则
- 返回标准的JSON响应格式
- 提供完整的错误信息

## 调试技巧

### 1. 日志记录
```python
self.logger.info("信息日志")
self.logger.warning("警告日志")
self.logger.error("错误日志")
```

### 2. 配置调试
```python
config = self.app.plugin_manager.get_plugin_config(self.get_name())
self.logger.debug(f"当前配置: {config}")
```

### 3. 状态检查
访问 `/api/system/plugins/status` 查看插件状态

## 常见问题

### Q: 插件无法加载？
A: 检查以下几点：
- 插件目录结构是否正确
- plugin.py 文件是否存在
- Plugin 类是否正确继承 BasePlugin
- 是否有语法错误

### Q: 路由无法访问？
A: 检查以下几点：
- 路由配置是否正确
- 视图函数是否存在
- 插件是否成功加载

### Q: 配置无法保存？
A: 检查以下几点：
- 配置模式定义是否正确
- 配置验证逻辑是否有问题
- 是否有权限问题

## 更多资源

- [平台API文档](../../docs/api.md)
- [开发指南](../../docs/development.md)
- [示例插件](../example_plugin/)
