{% extends "base.html" %}

{% block title %}错误 {{ error_code }} - {{ app_name }}{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8 col-lg-6">
        <div class="text-center">
            <!-- 错误图标 -->
            <div class="mb-4">
                {% if error_code == 404 %}
                <i class="fas fa-search fa-5x text-muted"></i>
                {% elif error_code == 500 %}
                <i class="fas fa-exclamation-triangle fa-5x text-warning"></i>
                {% else %}
                <i class="fas fa-times-circle fa-5x text-danger"></i>
                {% endif %}
            </div>
            
            <!-- 错误信息 -->
            <h1 class="display-1 fw-bold text-muted">{{ error_code }}</h1>
            <h2 class="mb-3">
                {% if error_code == 404 %}
                页面未找到
                {% elif error_code == 500 %}
                服务器内部错误
                {% else %}
                发生错误
                {% endif %}
            </h2>
            
            <p class="lead text-muted mb-4">
                {% if error_message %}
                {{ error_message }}
                {% else %}
                    {% if error_code == 404 %}
                    抱歉，您访问的页面不存在或已被移除。
                    {% elif error_code == 500 %}
                    服务器遇到了一个错误，无法完成您的请求。
                    {% else %}
                    系统遇到了一个未知错误。
                    {% endif %}
                {% endif %}
            </p>
            
            <!-- 操作按钮 -->
            <div class="d-flex justify-content-center gap-3">
                <a href="{{ url_for('index') }}" class="btn btn-primary">
                    <i class="fas fa-home me-2"></i>返回首页
                </a>
                <button onclick="history.back()" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-2"></i>返回上页
                </button>
                {% if error_code == 500 %}
                <a href="/health" target="_blank" class="btn btn-outline-info">
                    <i class="fas fa-heartbeat me-2"></i>系统状态
                </a>
                {% endif %}
            </div>
            
            <!-- 帮助信息 -->
            <div class="mt-5">
                <div class="card">
                    <div class="card-body">
                        <h6 class="card-title">
                            <i class="fas fa-lightbulb me-2"></i>可能的解决方案
                        </h6>
                        <ul class="list-unstyled text-start">
                            {% if error_code == 404 %}
                            <li><i class="fas fa-check text-success me-2"></i>检查URL地址是否正确</li>
                            <li><i class="fas fa-check text-success me-2"></i>确认页面是否存在</li>
                            <li><i class="fas fa-check text-success me-2"></i>尝试从首页重新导航</li>
                            {% elif error_code == 500 %}
                            <li><i class="fas fa-check text-success me-2"></i>稍后重试</li>
                            <li><i class="fas fa-check text-success me-2"></i>检查系统状态</li>
                            <li><i class="fas fa-check text-success me-2"></i>联系系统管理员</li>
                            {% else %}
                            <li><i class="fas fa-check text-success me-2"></i>刷新页面重试</li>
                            <li><i class="fas fa-check text-success me-2"></i>清除浏览器缓存</li>
                            <li><i class="fas fa-check text-success me-2"></i>检查网络连接</li>
                            {% endif %}
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- 系统信息 -->
            <div class="mt-4">
                <small class="text-muted">
                    {{ app_name }} v{{ app_version }} | 
                    <a href="/api/system/info" target="_blank" class="text-decoration-none">系统信息</a>
                </small>
            </div>
        </div>
    </div>
</div>
{% endblock %}
