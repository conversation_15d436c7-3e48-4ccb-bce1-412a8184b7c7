{% extends "base.html" %}

{% block title %}首页 - {{ app_name }}{% endblock %}

{% block page_title %}首页{% endblock %}

{% block content %}
<!-- 插件列表 -->
<div class="plugins-section">
    <div class="section-header">
        <h3 class="section-title">
            <i class="fas fa-puzzle-piece me-2"></i>插件列表
        </h3>
        <p class="section-subtitle">当前系统中已加载 {{ plugin_count }} 个插件模块</p>
    </div>

    {% if plugins %}
    <div class="table-responsive shadow-sm">
        <table class="table table-hover table-striped mb-0">
            <thead class="table-dark">
                <tr>
                    <th scope="col" class="border-0">
                        <i class="fas fa-puzzle-piece me-2"></i>插件名称
                    </th>
                    <th scope="col" class="border-0">
                        <i class="fas fa-tag me-2"></i>版本
                    </th>
                    <th scope="col" class="border-0">
                        <i class="fas fa-user me-2"></i>作者
                    </th>
                    <th scope="col" class="border-0">
                        <i class="fas fa-info-circle me-2"></i>功能描述
                    </th>
                    <th scope="col" class="border-0">
                        <i class="fas fa-cogs me-2"></i>操作
                    </th>
                </tr>
            </thead>
            <tbody>
                {% for plugin in plugins %}
                <tr class="align-middle">
                    <td class="py-3">
                        <div class="plugin-name-cell">
                            <div class="d-flex align-items-center">
                                <div class="plugin-icon me-3">
                                    <i class="fas fa-puzzle-piece text-primary fs-4"></i>
                                </div>
                                <div>
                                    <div class="fw-bold text-dark">{{ plugin.displayName }}</div>
                                    {% if plugin.name != plugin.displayName %}
                                    <small class="text-muted">{{ plugin.name }}</small>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </td>
                    <td class="py-3">
                        <span class="badge bg-primary fs-6">v{{ plugin.version }}</span>
                    </td>
                    <td class="py-3">
                        <span class="text-secondary">{{ plugin.author }}</span>
                    </td>
                    <td class="py-3">
                        <span class="plugin-description text-dark">{{ plugin.description or '暂无描述' }}</span>
                    </td>
                    <td class="py-3">
                        {% if plugin.navItems %}
                        <div class="btn-group" role="group">
                            {% for navItem in plugin.navItems %}
                            <a href="{{ navItem.url }}" class="btn btn-sm btn-primary" title="{{ navItem.title }}">
                                {% if navItem.get('icon') %}
                                <i class="{{ navItem.icon }} me-1"></i>
                                {% else %}
                                <i class="fas fa-external-link-alt me-1"></i>
                                {% endif %}
                                {{ navItem.title }}
                            </a>
                            {% endfor %}
                        </div>
                        {% else %}
                        <span class="text-muted">
                            <i class="fas fa-minus-circle me-1"></i>无功能入口
                        </span>
                        {% endif %}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
    {% else %}
    <div class="empty-state text-center py-5">
        <div class="empty-icon mb-3">
            <i class="fas fa-puzzle-piece fa-4x text-muted"></i>
        </div>
        <h4 class="text-muted">暂无插件</h4>
        <p class="text-muted">系统中还没有加载任何插件模块</p>
        <div class="mt-3">
            <small class="text-muted">
                <i class="fas fa-info-circle me-1"></i>
                请在 plugins 目录中添加插件来扩展系统功能
            </small>
        </div>
    </div>
    {% endif %}
</div>

{% endblock %}
