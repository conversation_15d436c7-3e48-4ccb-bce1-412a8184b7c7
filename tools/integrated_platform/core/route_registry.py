#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
路由注册器
"""

from typing import Dict, List, Any, Callable, Optional
import logging
from flask import Flask


class RouteRegistry:
    """路由注册器"""
    
    def __init__(self, app: Flask = None):
        self.app = app
        self.logger = logging.getLogger('route_registry')
        
        # 路由存储
        self._plugin_routes: Dict[str, List[Dict[str, Any]]] = {}
        self._registered_routes: Dict[str, List[str]] = {}
        
    def register_plugin_routes(self, plugin_name: str, routes: List[Dict[str, Any]]) -> bool:
        """注册插件路由"""
        try:
            if not self.app:
                self.logger.error("Flask应用未初始化")
                return False
                
            registered_endpoints = []
            
            for route_config in routes:
                endpoint = self._register_single_route(plugin_name, route_config)
                if endpoint:
                    registered_endpoints.append(endpoint)
                else:
                    # 如果有路由注册失败，回滚已注册的路由
                    self._unregister_endpoints(registered_endpoints)
                    return False
                    
            # 存储插件路由信息
            self._plugin_routes[plugin_name] = routes
            self._registered_routes[plugin_name] = registered_endpoints
            
            self.logger.info(f"插件路由注册成功: {plugin_name}, 注册了 {len(routes)} 个路由")
            return True
            
        except Exception as e:
            self.logger.error(f"注册插件路由失败: {plugin_name}, 错误: {str(e)}")
            return False
            
    def _register_single_route(self, plugin_name: str, route_config: Dict[str, Any]) -> Optional[str]:
        """注册单个路由"""
        try:
            # 验证路由配置
            if not self._validate_route_config(route_config):
                return None
                
            rule = route_config['rule']
            view_func = route_config['view_func']
            methods = route_config.get('methods', ['GET'])
            endpoint = route_config.get('endpoint')
            
            # 生成端点名称
            if not endpoint:
                endpoint = f"plugin_{plugin_name}_{view_func.__name__}"
            else:
                endpoint = f"plugin_{plugin_name}_{endpoint}"
                
            # 检查端点是否已存在
            if endpoint in self.app.view_functions:
                self.logger.error(f"端点已存在: {endpoint}")
                return None
                
            # 添加路由规则前缀
            if not rule.startswith('/'):
                rule = '/' + rule
            rule = f"/plugins/{plugin_name}{rule}"
            
            # 注册路由
            self.app.add_url_rule(
                rule=rule,
                endpoint=endpoint,
                view_func=view_func,
                methods=methods,
                **route_config.get('options', {})
            )
            
            self.logger.debug(f"路由注册成功: {rule} -> {endpoint}")
            return endpoint
            
        except Exception as e:
            self.logger.error(f"注册单个路由失败: {str(e)}")
            return None
            
    def _validate_route_config(self, route_config: Dict[str, Any]) -> bool:
        """验证路由配置"""
        required_fields = ['rule', 'view_func']
        
        for field in required_fields:
            if field not in route_config:
                self.logger.error(f"路由配置缺少必需字段: {field}")
                return False
                
        if not callable(route_config['view_func']):
            self.logger.error("view_func必须是可调用对象")
            return False
            
        return True
        
    def unregister_plugin_routes(self, plugin_name: str) -> bool:
        """注销插件路由"""
        try:
            if plugin_name not in self._registered_routes:
                self.logger.warning(f"插件路由未注册: {plugin_name}")
                return True
                
            endpoints = self._registered_routes[plugin_name]
            self._unregister_endpoints(endpoints)
            
            # 清理存储的路由信息
            if plugin_name in self._plugin_routes:
                del self._plugin_routes[plugin_name]
            if plugin_name in self._registered_routes:
                del self._registered_routes[plugin_name]
                
            self.logger.info(f"插件路由注销成功: {plugin_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"注销插件路由失败: {plugin_name}, 错误: {str(e)}")
            return False
            
    def _unregister_endpoints(self, endpoints: List[str]):
        """注销端点"""
        for endpoint in endpoints:
            try:
                if endpoint in self.app.view_functions:
                    # 从视图函数中移除
                    del self.app.view_functions[endpoint]
                    
                    # 从URL映射中移除相关规则
                    rules_to_remove = []
                    for rule in self.app.url_map.iter_rules():
                        if rule.endpoint == endpoint:
                            rules_to_remove.append(rule)
                            
                    for rule in rules_to_remove:
                        self.app.url_map._rules.remove(rule)
                        if rule in self.app.url_map._rules_by_endpoint.get(endpoint, []):
                            self.app.url_map._rules_by_endpoint[endpoint].remove(rule)
                            
                    # 清理空的端点映射
                    if endpoint in self.app.url_map._rules_by_endpoint and not self.app.url_map._rules_by_endpoint[endpoint]:
                        del self.app.url_map._rules_by_endpoint[endpoint]
                        
                    self.logger.debug(f"端点注销成功: {endpoint}")
                    
            except Exception as e:
                self.logger.error(f"注销端点失败: {endpoint}, 错误: {str(e)}")
                
    def get_plugin_routes(self, plugin_name: str) -> List[Dict[str, Any]]:
        """获取插件路由"""
        return self._plugin_routes.get(plugin_name, [])
        
    def get_all_plugin_routes(self) -> Dict[str, List[Dict[str, Any]]]:
        """获取所有插件路由"""
        return self._plugin_routes.copy()
        
    def get_registered_endpoints(self, plugin_name: str) -> List[str]:
        """获取插件已注册的端点"""
        return self._registered_routes.get(plugin_name, [])
        
    def is_plugin_routes_registered(self, plugin_name: str) -> bool:
        """检查插件路由是否已注册"""
        return plugin_name in self._registered_routes
        
    def get_route_info(self) -> Dict[str, Any]:
        """获取路由信息"""
        info = {
            'total_plugins': len(self._plugin_routes),
            'total_routes': sum(len(routes) for routes in self._plugin_routes.values()),
            'plugins': {}
        }
        
        for plugin_name, routes in self._plugin_routes.items():
            info['plugins'][plugin_name] = {
                'route_count': len(routes),
                'endpoints': self._registered_routes.get(plugin_name, []),
                'routes': [
                    {
                        'rule': route['rule'],
                        'methods': route.get('methods', ['GET']),
                        'endpoint': route.get('endpoint', 'auto-generated')
                    }
                    for route in routes
                ]
            }
            
        return info
        
    def list_all_routes(self) -> List[Dict[str, Any]]:
        """列出所有路由"""
        routes = []
        
        for plugin_name, plugin_routes in self._plugin_routes.items():
            for route_config in plugin_routes:
                rule = route_config['rule']
                if not rule.startswith('/'):
                    rule = '/' + rule
                full_rule = f"/plugins/{plugin_name}{rule}"
                
                routes.append({
                    'plugin': plugin_name,
                    'rule': full_rule,
                    'methods': route_config.get('methods', ['GET']),
                    'endpoint': route_config.get('endpoint', 'auto-generated'),
                    'view_func': route_config['view_func'].__name__
                })
                
        return routes
        
    def clear_all_routes(self) -> bool:
        """清理所有插件路由"""
        try:
            plugin_names = list(self._registered_routes.keys())
            
            for plugin_name in plugin_names:
                self.unregister_plugin_routes(plugin_name)
                
            self.logger.info("所有插件路由清理完成")
            return True
            
        except Exception as e:
            self.logger.error(f"清理所有插件路由失败: {str(e)}")
            return False
