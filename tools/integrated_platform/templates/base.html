<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{{ app_name }}{% endblock %}</title>

    <!-- Bootstrap CSS (本地) -->
    <link href="{{ url_for('static', filename='css/vendor/bootstrap.min.css') }}" rel="stylesheet">
    <!-- Font Awesome (本地) -->
    <link href="{{ url_for('static', filename='css/vendor/fontawesome.min.css') }}" rel="stylesheet">
    <!-- 自定义CSS -->
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">

    {% block extra_css %}{% endblock %}
</head>
<body class="tech-layout">
    <!-- 左侧导航栏 -->
    <nav class="sidebar" id="sidebar">
        <!-- Logo区域 -->
        <div class="sidebar-header">
            <div class="logo-container">
                <div class="logo-icon">
                    <i class="fas fa-cube"></i>
                </div>
                <div class="logo-text">
                    <h4 class="mb-0">{{ app_name }}</h4>
                    <small class="text-muted">v{{ app_version }}</small>
                </div>
            </div>
            <button class="sidebar-toggle d-lg-none" id="sidebarToggle">
                <i class="fas fa-times"></i>
            </button>
        </div>

        <!-- 导航菜单 -->
        <div class="sidebar-menu">
            <!-- 主导航 -->
            <div class="menu-section">
                <div class="menu-title">主要功能</div>
                <ul class="nav-list">
                    <li class="nav-item">
                        <a href="{{ url_for('index') }}" class="nav-link active">
                            <div class="nav-icon">
                                <i class="fas fa-home"></i>
                            </div>
                            <span class="nav-text">首页</span>
                        </a>
                    </li>
                </ul>
            </div>

            <!-- 插件导航 -->
            {% if plugin_nav_items %}
            <div class="menu-section">
                <div class="menu-title">插件功能</div>
                <ul class="nav-list">
                    {% for nav_item in plugin_nav_items %}
                    <li class="nav-item">
                        {% if nav_item.get('dropdown') %}
                        <a href="#" class="nav-link dropdown-toggle" data-bs-toggle="collapse" data-bs-target="#plugin-{{ loop.index }}">
                            <div class="nav-icon">
                                <i class="{{ nav_item.get('icon', 'fas fa-puzzle-piece') }}"></i>
                            </div>
                            <span class="nav-text">{{ nav_item.title }}</span>
                            <i class="fas fa-chevron-down dropdown-arrow"></i>
                        </a>
                        <div class="collapse" id="plugin-{{ loop.index }}">
                            <ul class="sub-nav-list">
                                {% for sub_item in nav_item.dropdown %}
                                <li class="sub-nav-item">
                                    <a href="{{ sub_item.url }}" class="sub-nav-link">
                                        <i class="{{ sub_item.get('icon', 'fas fa-circle') }}"></i>
                                        {{ sub_item.title }}
                                    </a>
                                </li>
                                {% endfor %}
                            </ul>
                        </div>
                        {% else %}
                        <a href="{{ nav_item.url }}" class="nav-link">
                            <div class="nav-icon">
                                <i class="{{ nav_item.get('icon', 'fas fa-puzzle-piece') }}"></i>
                            </div>
                            <span class="nav-text">{{ nav_item.title }}</span>
                        </a>
                        {% endif %}
                    </li>
                    {% endfor %}
                </ul>
            </div>
            {% endif %}


        </div>

        <!-- 底部信息 -->
        <div class="sidebar-footer">
            <div class="status-info">
                <div class="status-item">
                    <i class="fas fa-puzzle-piece me-1"></i>
                    <span class="status-text">{{ plugin_count }} 个插件</span>
                </div>
                <div class="status-item">
                    <i class="fas fa-code me-1"></i>
                    <span class="status-text">v{{ app_version }}</span>
                </div>
            </div>
        </div>
    </nav>

    <!-- 移动端导航切换按钮 -->
    <button class="mobile-nav-toggle d-lg-none" id="mobileNavToggle">
        <i class="fas fa-bars"></i>
    </button>

    <!-- 主内容区域 -->
    <main class="main-content">
        <!-- 内容区域 -->
        <div class="content-area">
            <!-- 消息提示 -->
            {% with messages = get_flashed_messages(with_categories=true) %}
                {% if messages %}
                    {% for category, message in messages %}
                        <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}
            {% endwith %}

            <!-- 页面内容 -->
            {% block content %}{% endblock %}
        </div>
    </main>

    <!-- Bootstrap JS (本地) -->
    <script src="{{ url_for('static', filename='js/vendor/bootstrap.bundle.min.js') }}"></script>
    <!-- jQuery (本地) -->
    <script src="{{ url_for('static', filename='js/vendor/jquery.min.js') }}"></script>
    <!-- 自定义JS -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>

    {% block extra_js %}{% endblock %}
</body>
</html>
