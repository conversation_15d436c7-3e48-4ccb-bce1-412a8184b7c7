{% extends "base.html" %}

{% block title %}报文修改工具 - {{ app_name }}{% endblock %}

{% block extra_css %}
<style>
.upload-area {
    border: 2px dashed #007bff;
    border-radius: 10px;
    padding: 40px;
    text-align: center;
    background: rgba(0, 123, 255, 0.05);
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-area:hover {
    border-color: #0056b3;
    background: rgba(0, 123, 255, 0.1);
}

.upload-area.dragover {
    border-color: #28a745;
    background: rgba(40, 167, 69, 0.1);
}

.file-list {
    max-height: 300px;
    overflow-y: auto;
}

.file-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    margin-bottom: 5px;
    background: #f8f9fa;
}

.progress-container {
    display: none;
}

.result-container {
    display: none;
}

.ip-input {
    font-family: 'Courier New', monospace;
}

.step-indicator {
    display: flex;
    justify-content: space-between;
    margin-bottom: 30px;
}

.step {
    flex: 1;
    text-align: center;
    padding: 10px;
    border-radius: 5px;
    margin: 0 5px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
}

.step.active {
    background: #007bff;
    color: white;
    border-color: #007bff;
}

.step.completed {
    background: #28a745;
    color: white;
    border-color: #28a745;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-network-wired me-2"></i>报文修改工具
                    </h4>
                    <small>批量修改PCAP文件的源IP和目标IP地址</small>
                </div>
                
                <div class="card-body">
                    <!-- 步骤指示器 -->
                    <div class="step-indicator">
                        <div class="step active" id="step1">
                            <i class="fas fa-upload"></i>
                            <div>1. 上传文件</div>
                        </div>
                        <div class="step" id="step2">
                            <i class="fas fa-cog"></i>
                            <div>2. 配置参数</div>
                        </div>
                        <div class="step" id="step3">
                            <i class="fas fa-play"></i>
                            <div>3. 处理文件</div>
                        </div>
                        <div class="step" id="step4">
                            <i class="fas fa-download"></i>
                            <div>4. 下载结果</div>
                        </div>
                    </div>

                    <!-- 文件上传区域 -->
                    <div id="uploadSection">
                        <h5 class="mb-3">
                            <i class="fas fa-file-upload me-2"></i>选择PCAP文件
                        </h5>
                        
                        <div class="upload-area" id="uploadArea">
                            <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-3"></i>
                            <h5>拖拽文件到此处或点击选择</h5>
                            <p class="text-muted">支持 .pcap, .cap, .pcapng 格式文件</p>
                            <p class="text-muted">可以选择多个文件进行批量处理</p>
                            <input type="file" id="fileInput" multiple accept=".pcap,.cap,.pcapng" style="display: none;">
                            <button type="button" class="btn btn-primary mt-3" onclick="testFileInput();">
                                <i class="fas fa-folder-open me-2"></i>选择文件
                            </button>
                        </div>
                        
                        <div id="fileListContainer" style="display: none;">
                            <h6 class="mt-4 mb-3">已选择的文件：</h6>
                            <div class="file-list" id="fileList"></div>
                            <div class="mt-3">
                                <button class="btn btn-primary" id="uploadBtn">
                                    <i class="fas fa-upload me-2"></i>上传文件
                                </button>
                                <button class="btn btn-secondary ms-2" id="clearBtn">
                                    <i class="fas fa-trash me-2"></i>清空列表
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 参数配置区域 -->
                    <div id="configSection" style="display: none;">
                        <h5 class="mb-3">
                            <i class="fas fa-cogs me-2"></i>配置IP地址参数
                        </h5>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="srcIp" class="form-label">
                                        <i class="fas fa-arrow-right me-2"></i>源IP地址 (Source IP)
                                    </label>
                                    <input type="text" class="form-control ip-input" id="srcIp" 
                                           placeholder="例如: ***********" value="***********">
                                    <div class="form-text">
                                        多个文件时会自动递增，如 ***********, ***********, ...
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="dstIp" class="form-label">
                                        <i class="fas fa-bullseye me-2"></i>目标IP地址 (Destination IP)
                                    </label>
                                    <input type="text" class="form-control ip-input" id="dstIp" 
                                           placeholder="例如: ********" value="********">
                                    <div class="form-text">
                                        多个文件时会自动递增，如 ********, ********, ...
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-4">
                            <button class="btn btn-success" id="processBtn">
                                <i class="fas fa-play me-2"></i>开始处理
                            </button>
                            <button class="btn btn-secondary ms-2" id="backToUploadBtn">
                                <i class="fas fa-arrow-left me-2"></i>返回上传
                            </button>
                        </div>
                    </div>

                    <!-- 处理进度区域 -->
                    <div class="progress-container" id="progressSection">
                        <h5 class="mb-3">
                            <i class="fas fa-spinner fa-spin me-2"></i>正在处理文件...
                        </h5>
                        <div class="progress mb-3">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                 role="progressbar" style="width: 0%"></div>
                        </div>
                        <div id="progressText">准备开始处理...</div>
                    </div>

                    <!-- 结果显示区域 -->
                    <div class="result-container" id="resultSection">
                        <h5 class="mb-3">
                            <i class="fas fa-check-circle text-success me-2"></i>处理完成
                        </h5>
                        <div class="alert alert-success" id="resultInfo"></div>
                        <div class="mt-3">
                            <a href="javascript:void(0)" class="btn btn-primary" id="downloadBtn">
                                <i class="fas fa-download me-2"></i>下载处理结果
                            </a>
                            <button class="btn btn-secondary ms-2" id="newTaskBtn">
                                <i class="fas fa-plus me-2"></i>开始新任务
                            </button>
                            <button class="btn btn-outline-warning ms-2" id="cleanupBtn" title="清理历史文件">
                                <i class="fas fa-trash-alt me-2"></i>清理文件
                            </button>
                        </div>
                    </div>

                    <!-- 错误提示区域 -->
                    <div id="errorAlert" class="alert alert-danger" style="display: none;">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <span id="errorMessage"></span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 全局变量
let selectedFiles = [];
let currentTaskId = null;

// 全局测试函数
function testFileInput() {
    console.log('测试按钮被点击');
    try {
        const fileInput = document.getElementById('fileInput');
        if (fileInput) {
            console.log('找到文件输入框，触发点击');
            fileInput.click();
        } else {
            console.error('未找到文件输入框');
        }
    } catch (error) {
        console.error('测试函数错误:', error);
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('pcapChange页面JavaScript已加载（原生JS版本）');
    initializeUpload();
});

function initializeUpload() {
    // 获取DOM元素
    const uploadArea = document.getElementById('uploadArea');
    const fileInput = document.getElementById('fileInput');

    console.log('uploadArea元素:', uploadArea ? '找到' : '未找到');
    console.log('fileInput元素:', fileInput ? '找到' : '未找到');

    // 检查元素是否找到
    if (!uploadArea) {
        console.error('未找到uploadArea元素');
        return;
    }

    if (!fileInput) {
        console.error('未找到fileInput元素');
        return;
    }

    // 绑定事件
    bindEvents(uploadArea, fileInput);
}

function bindEvents(uploadArea, fileInput) {
    // 点击上传区域选择文件 - 修复重复点击问题
    uploadArea.addEventListener('click', function(e) {
        console.log('上传区域被点击');
        // 防止按钮点击事件冒泡到上传区域
        if (e.target.tagName === 'BUTTON' || e.target.closest('button')) {
            return;
        }
        try {
            fileInput.click();
        } catch (error) {
            console.error('点击文件输入框失败:', error);
        }
    });

    // 拖拽事件
    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        e.stopPropagation();
        uploadArea.classList.add('dragover');
    });

    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        e.stopPropagation();
        uploadArea.classList.remove('dragover');
    });

    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        e.stopPropagation();
        uploadArea.classList.remove('dragover');

        try {
            const files = e.dataTransfer.files;
            console.log('拖拽文件数量:', files.length);
            if (files && files.length > 0) {
                handleFileSelection(files);
            }
        } catch (error) {
            console.error('处理拖拽文件失败:', error);
        }
    });

    // 文件选择事件
    fileInput.addEventListener('change', function(e) {
        console.log('文件选择事件触发');
        try {
            const files = e.target.files;
            console.log('文件数量:', files ? files.length : 0);
            if (files && files.length > 0) {
                console.log('开始处理选择的文件');
                handleFileSelection(files);
            } else {
                console.log('没有选择文件');
            }
        } catch (error) {
            console.error('处理文件选择事件失败:', error);
        }
    });

    // 绑定按钮事件
    bindButtonEvents();
}

function bindButtonEvents() {
    // 上传按钮
    const uploadBtn = document.getElementById('uploadBtn');
    if (uploadBtn) {
        uploadBtn.addEventListener('click', function() {
            if (selectedFiles.length === 0) {
                showError('请先选择文件');
                return;
            }
            uploadFiles();
        });
    }

    // 清空按钮
    const clearBtn = document.getElementById('clearBtn');
    if (clearBtn) {
        clearBtn.addEventListener('click', function() {
            selectedFiles = [];
            updateFileList();
            document.getElementById('fileInput').value = '';
        });
    }

    // 返回上传按钮
    const backToUploadBtn = document.getElementById('backToUploadBtn');
    if (backToUploadBtn) {
        backToUploadBtn.addEventListener('click', function() {
            document.getElementById('configSection').style.display = 'none';
            document.getElementById('uploadSection').style.display = 'block';
            updateStep(1);
        });
    }

    // 开始处理按钮
    const processBtn = document.getElementById('processBtn');
    if (processBtn) {
        processBtn.addEventListener('click', function() {
            console.log('开始处理按钮被点击');
            const srcIp = document.getElementById('srcIp').value.trim();
            const dstIp = document.getElementById('dstIp').value.trim();

            console.log('源IP:', srcIp, '目标IP:', dstIp);

            if (!validateIpAddress(srcIp)) {
                showError('源IP地址格式不正确');
                return;
            }

            if (!validateIpAddress(dstIp)) {
                showError('目标IP地址格式不正确');
                return;
            }

            processFiles(srcIp, dstIp);
        });
    }

    // 新任务按钮
    const newTaskBtn = document.getElementById('newTaskBtn');
    if (newTaskBtn) {
        newTaskBtn.addEventListener('click', function() {
            startNewTask();
        });
    }

    // 清理文件按钮
    const cleanupBtn = document.getElementById('cleanupBtn');
    if (cleanupBtn) {
        cleanupBtn.addEventListener('click', function() {
            cleanupFiles();
        });
    }
}

// 处理文件选择
function handleFileSelection(files) {
    try {
        console.log('处理文件选择，文件数量:', files ? files.length : 0);
        console.log('当前已选择文件数量:', selectedFiles.length);

        if (!files || files.length === 0) {
            console.log('没有文件需要处理');
            return;
        }

        let addedCount = 0;
        for (let i = 0; i < files.length; i++) {
            const file = files[i];
            console.log('处理文件:', file.name, '大小:', file.size);

            if (validateFile(file)) {
                selectedFiles.push(file);
                addedCount++;
                console.log('文件验证通过:', file.name);
            } else {
                console.log('文件验证失败:', file.name);
            }
        }

        console.log('本次添加文件数量:', addedCount);
        console.log('总文件数量:', selectedFiles.length);
        updateFileList();
    } catch (error) {
        console.error('处理文件选择失败:', error);
        showError('处理文件选择失败: ' + error.message);
    }
}

// 验证文件类型
function validateFile(file) {
    try {
        if (!file || !file.name) {
            console.error('无效的文件对象');
            showError('无效的文件');
            return false;
        }

        const allowedTypes = ['.pcap', '.cap', '.pcapng'];
        const fileName = file.name.toLowerCase();
        const isValid = allowedTypes.some(type => fileName.endsWith(type));

        if (!isValid) {
            showError(`不支持的文件类型: ${file.name}`);
            return false;
        }

        // 检查是否已存在
        const exists = selectedFiles.some(f => f.name === file.name && f.size === file.size);
        if (exists) {
            showError(`文件已存在: ${file.name}`);
            return false;
        }

        return true;
    } catch (error) {
        console.error('文件验证失败:', error);
        showError('文件验证失败: ' + error.message);
        return false;
    }
}

// 更新文件列表显示
function updateFileList() {
    const fileList = document.getElementById('fileList');
    const container = document.getElementById('fileListContainer');

    if (!fileList || !container) {
        console.error('未找到文件列表元素');
        return;
    }

    if (selectedFiles.length === 0) {
        container.style.display = 'none';
        return;
    }

    container.style.display = 'block';
    fileList.innerHTML = '';

    selectedFiles.forEach((file, index) => {
        const fileItem = document.createElement('div');
        fileItem.className = 'file-item';
        fileItem.innerHTML = `
            <div>
                <i class="fas fa-file me-2"></i>
                <strong>${file.name}</strong>
                <small class="text-muted ms-2">(${formatFileSize(file.size)})</small>
            </div>
            <button class="btn btn-sm btn-outline-danger" onclick="removeFile(${index})">
                <i class="fas fa-times"></i>
            </button>
        `;
        fileList.appendChild(fileItem);
    });
}

// 移除文件
window.removeFile = function(index) {
    selectedFiles.splice(index, 1);
    updateFileList();
};

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 上传文件
function uploadFiles() {
    console.log('开始上传文件，文件数量:', selectedFiles.length);

    if (selectedFiles.length === 0) {
        showError('没有选择文件');
        return;
    }

    const formData = new FormData();
    selectedFiles.forEach((file, index) => {
        console.log(`添加文件 ${index + 1}: ${file.name}`);
        formData.append('files', file);
    });

    showProgress('正在上传文件...', 20);

    fetch('/api/plugins/pcapChange/upload', {
        method: 'POST',
        body: formData
    })
    .then(response => {
        console.log('上传响应状态:', response.status);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('上传响应数据:', data);
        if (data.success) {
            currentTaskId = data.data.taskId;
            console.log('获得任务ID:', currentTaskId);
            showConfigSection();
            updateStep(2);
        } else {
            console.error('上传失败:', data.message);
            showError(data.message || '上传失败');
        }
        hideProgress();
    })
    .catch(error => {
        console.error('上传失败:', error);
        showError('上传失败: ' + error.message);
        hideProgress();
    });
}
// 显示/隐藏区域
function showConfigSection() {
    document.getElementById('uploadSection').style.display = 'none';
    document.getElementById('configSection').style.display = 'block';
}

function showProgress(text, percent) {
    const progressSection = document.getElementById('progressSection');
    const progressText = document.getElementById('progressText');
    const progressBar = document.querySelector('.progress-bar');

    if (progressText) progressText.textContent = text;
    if (progressBar) progressBar.style.width = percent + '%';
    if (progressSection) progressSection.style.display = 'block';
}

function hideProgress() {
    const progressSection = document.getElementById('progressSection');
    if (progressSection) progressSection.style.display = 'none';
}

function updateStep(step) {
    const steps = document.querySelectorAll('.step');
    steps.forEach((stepEl, index) => {
        stepEl.classList.remove('active', 'completed');
        if (index + 1 < step) {
            stepEl.classList.add('completed');
        } else if (index + 1 === step) {
            stepEl.classList.add('active');
        }
    });
}

function showError(message) {
    const errorAlert = document.getElementById('errorAlert');
    const errorMessage = document.getElementById('errorMessage');

    if (errorMessage) errorMessage.textContent = message;
    if (errorAlert) {
        errorAlert.style.display = 'block';
        setTimeout(() => {
            errorAlert.style.display = 'none';
        }, 5000);
    }
}

// 验证IP地址格式
function validateIpAddress(ip) {
    const pattern = /^(\d{1,3}\.){3}\d{1,3}$/;
    if (!pattern.test(ip)) return false;

    const parts = ip.split('.');
    return parts.every(part => {
        const num = parseInt(part);
        return num >= 0 && num <= 255;
    });
}

// 处理文件
function processFiles(srcIp, dstIp) {
    console.log('开始处理文件，源IP:', srcIp, '目标IP:', dstIp, '任务ID:', currentTaskId);

    if (!currentTaskId) {
        showError('任务ID不存在，请重新上传文件');
        return;
    }

    document.getElementById('configSection').style.display = 'none';
    showProgress('正在处理文件...', 50);
    updateStep(3);

    const requestData = {
        taskId: currentTaskId,
        srcIp: srcIp,
        dstIp: dstIp
    };

    console.log('发送处理请求:', requestData);

    fetch('/api/plugins/pcapChange/process', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
    })
    .then(response => {
        console.log('处理响应状态:', response.status);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('处理响应数据:', data);
        if (data.success) {
            showResult(data.data);
            updateStep(4);
        } else {
            console.error('处理失败:', data.message);
            showError(data.message || '处理失败');
            // 显示配置区域，让用户可以重新尝试
            document.getElementById('configSection').style.display = 'block';
            updateStep(2);
        }
        hideProgress();
    })
    .catch(error => {
        console.error('处理请求失败:', error);
        showError('处理失败: ' + error.message);
        // 显示配置区域，让用户可以重新尝试
        document.getElementById('configSection').style.display = 'block';
        updateStep(2);
        hideProgress();
    });
}

// 显示处理结果
function showResult(data) {
    const resultInfo = document.getElementById('resultInfo');
    const downloadBtn = document.getElementById('downloadBtn');
    const resultSection = document.getElementById('resultSection');

    if (resultInfo) {
        resultInfo.innerHTML = `
            <strong>处理完成！</strong><br>
            成功处理了 ${data.processedCount} 个文件<br>
            任务ID: ${data.taskId}
        `;
    }

    if (downloadBtn) {
        downloadBtn.href = '/api/plugins/pcapChange/download/' + data.taskId;
    }

    if (resultSection) {
        resultSection.style.display = 'block';
    }
}

// 开始新任务
function startNewTask() {
    // 重置所有状态
    selectedFiles = [];
    currentTaskId = null;

    const fileInput = document.getElementById('fileInput');
    const srcIp = document.getElementById('srcIp');
    const dstIp = document.getElementById('dstIp');

    if (fileInput) fileInput.value = '';
    if (srcIp) srcIp.value = '***********';
    if (dstIp) dstIp.value = '********';

    // 隐藏所有区域，显示上传区域
    document.getElementById('configSection').style.display = 'none';
    document.getElementById('resultSection').style.display = 'none';
    document.getElementById('uploadSection').style.display = 'block';
    document.getElementById('fileListContainer').style.display = 'none';

    updateStep(1);
    hideError();
}

function hideError() {
    const errorAlert = document.getElementById('errorAlert');
    if (errorAlert) {
        errorAlert.style.display = 'none';
    }
}

// 清理文件功能
function cleanupFiles() {
    if (!confirm('确定要清理历史任务文件吗？\n\n这将删除过期的上传文件和处理结果，释放存储空间。\n当前任务不会受到影响。')) {
        return;
    }

    console.log('开始清理历史文件');
    showProgress('正在清理历史文件...', 30);

    fetch('/api/plugins/pcapChange/cleanup', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            force: false  // 按规则清理，不强制清理所有文件
        })
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('清理响应数据:', data);
        if (data.success) {
            showSuccess(data.message);
            console.log('清理完成，删除了', data.data.cleanedCount, '个任务的文件');
        } else {
            showError(data.message || '清理失败');
        }
        hideProgress();
    })
    .catch(error => {
        console.error('清理请求失败:', error);
        showError('清理失败: ' + error.message);
        hideProgress();
    });
}

// 显示成功消息
function showSuccess(message) {
    // 创建成功提示元素
    const successAlert = document.createElement('div');
    successAlert.className = 'alert alert-success alert-dismissible fade show';
    successAlert.innerHTML = `
        <i class="fas fa-check-circle me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // 插入到页面顶部
    const container = document.querySelector('.container-fluid');
    if (container) {
        container.insertBefore(successAlert, container.firstChild);

        // 5秒后自动隐藏
        setTimeout(() => {
            if (successAlert.parentNode) {
                successAlert.remove();
            }
        }, 5000);
    }
}

console.log('pcapChange插件JavaScript加载完成（原生JS版本）');
</script>
{% endblock %}
