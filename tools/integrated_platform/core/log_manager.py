#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志管理器
"""

import os
import logging
import logging.handlers
from typing import Dict, Any, Optional
from datetime import datetime


class LogManager:
    """日志管理器"""
    
    def __init__(self, app=None, log_dir: str = None):
        self.app = app
        self.log_dir = log_dir or os.path.join(os.path.dirname(os.path.dirname(__file__)), 'logs')
        
        # 确保日志目录存在
        os.makedirs(self.log_dir, exist_ok=True)
        
        # 日志配置
        self.log_level = logging.INFO
        self.log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        self.date_format = '%Y-%m-%d %H:%M:%S'
        
        # 插件日志记录器
        self._plugin_loggers: Dict[str, logging.Logger] = {}
        
        # 初始化根日志记录器
        self._setup_root_logger()

    @staticmethod
    def get_logger(name: str) -> logging.Logger:
        """获取日志记录器"""
        return logging.getLogger(name)
        
    def _setup_root_logger(self):
        """设置根日志记录器"""
        # 获取根日志记录器
        root_logger = logging.getLogger()
        root_logger.setLevel(self.log_level)
        
        # 清除现有处理器
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
            
        # 创建格式化器
        formatter = logging.Formatter(self.log_format, self.date_format)
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(self.log_level)
        console_handler.setFormatter(formatter)
        root_logger.addHandler(console_handler)
        
        # 文件处理器
        log_file = os.path.join(self.log_dir, 'app.log')
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        file_handler.setLevel(self.log_level)
        file_handler.setFormatter(formatter)
        root_logger.addHandler(file_handler)
        
        # 错误日志文件处理器
        error_log_file = os.path.join(self.log_dir, 'error.log')
        error_handler = logging.handlers.RotatingFileHandler(
            error_log_file,
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(formatter)
        root_logger.addHandler(error_handler)
        
    def setup_plugin_logger(self, plugin_name: str, log_level: Optional[int] = None) -> logging.Logger:
        """为插件设置专用日志记录器"""
        if plugin_name in self._plugin_loggers:
            return self._plugin_loggers[plugin_name]
            
        # 创建插件日志记录器
        logger_name = f"plugin.{plugin_name}"
        logger = logging.getLogger(logger_name)
        logger.setLevel(log_level or self.log_level)
        
        # 防止日志传播到根记录器（避免重复记录）
        logger.propagate = False
        
        # 创建格式化器
        formatter = logging.Formatter(
            f'%(asctime)s - {plugin_name} - %(levelname)s - %(message)s',
            self.date_format
        )
        
        # 控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(log_level or self.log_level)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
        
        # 插件专用日志文件
        plugin_log_file = os.path.join(self.log_dir, f'plugin_{plugin_name}.log')
        file_handler = logging.handlers.RotatingFileHandler(
            plugin_log_file,
            maxBytes=5*1024*1024,  # 5MB
            backupCount=3,
            encoding='utf-8'
        )
        file_handler.setLevel(log_level or self.log_level)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
        
        # 存储插件日志记录器
        self._plugin_loggers[plugin_name] = logger
        
        return logger
        
    def get_plugin_logger(self, plugin_name: str) -> Optional[logging.Logger]:
        """获取插件日志记录器"""
        return self._plugin_loggers.get(plugin_name)
        
    def remove_plugin_logger(self, plugin_name: str) -> bool:
        """移除插件日志记录器"""
        try:
            if plugin_name in self._plugin_loggers:
                logger = self._plugin_loggers[plugin_name]
                
                # 关闭所有处理器
                for handler in logger.handlers[:]:
                    handler.close()
                    logger.removeHandler(handler)
                    
                # 从字典中移除
                del self._plugin_loggers[plugin_name]
                
                return True
            return False
            
        except Exception as e:
            logging.getLogger(__name__).error(f"移除插件日志记录器失败: {plugin_name}, 错误: {str(e)}")
            return False
            
    def set_log_level(self, level: int):
        """设置全局日志级别"""
        self.log_level = level
        
        # 更新根日志记录器级别
        root_logger = logging.getLogger()
        root_logger.setLevel(level)
        
        # 更新所有处理器级别
        for handler in root_logger.handlers:
            if not isinstance(handler, logging.handlers.RotatingFileHandler) or 'error.log' not in handler.baseFilename:
                handler.setLevel(level)
                
    def set_plugin_log_level(self, plugin_name: str, level: int) -> bool:
        """设置插件日志级别"""
        try:
            if plugin_name in self._plugin_loggers:
                logger = self._plugin_loggers[plugin_name]
                logger.setLevel(level)
                
                # 更新处理器级别（除了错误处理器）
                for handler in logger.handlers:
                    handler.setLevel(level)
                    
                return True
            return False
            
        except Exception as e:
            logging.getLogger(__name__).error(f"设置插件日志级别失败: {plugin_name}, 错误: {str(e)}")
            return False
            
    def get_log_files(self) -> Dict[str, str]:
        """获取所有日志文件路径"""
        log_files = {}
        
        # 主日志文件
        log_files['app'] = os.path.join(self.log_dir, 'app.log')
        log_files['error'] = os.path.join(self.log_dir, 'error.log')
        
        # 插件日志文件
        for plugin_name in self._plugin_loggers:
            log_files[f'plugin_{plugin_name}'] = os.path.join(self.log_dir, f'plugin_{plugin_name}.log')
            
        return log_files
        
    def read_log_file(self, log_type: str, lines: int = 100) -> Optional[str]:
        """读取日志文件内容"""
        try:
            log_files = self.get_log_files()
            
            if log_type not in log_files:
                return None
                
            log_file = log_files[log_type]
            
            if not os.path.exists(log_file):
                return None
                
            with open(log_file, 'r', encoding='utf-8') as f:
                # 读取最后N行
                all_lines = f.readlines()
                return ''.join(all_lines[-lines:])
                
        except Exception as e:
            logging.getLogger(__name__).error(f"读取日志文件失败: {log_type}, 错误: {str(e)}")
            return None
            
    def get_log_stats(self) -> Dict[str, Any]:
        """获取日志统计信息"""
        stats = {
            'log_dir': self.log_dir,
            'log_level': logging.getLevelName(self.log_level),
            'plugin_loggers': len(self._plugin_loggers),
            'log_files': {}
        }
        
        log_files = self.get_log_files()
        
        for log_type, log_file in log_files.items():
            if os.path.exists(log_file):
                stat = os.stat(log_file)
                stats['log_files'][log_type] = {
                    'path': log_file,
                    'size': stat.st_size,
                    'modified': datetime.fromtimestamp(stat.st_mtime).isoformat()
                }
            else:
                stats['log_files'][log_type] = {
                    'path': log_file,
                    'exists': False
                }
                
        return stats
        
    def cleanup_old_logs(self, days: int = 30) -> int:
        """清理旧日志文件"""
        try:
            cleaned_count = 0
            current_time = datetime.now().timestamp()
            cutoff_time = current_time - (days * 24 * 60 * 60)
            
            for root, dirs, files in os.walk(self.log_dir):
                for file in files:
                    if file.endswith('.log') or file.endswith('.log.1') or file.endswith('.log.2'):
                        file_path = os.path.join(root, file)
                        
                        try:
                            file_stat = os.stat(file_path)
                            if file_stat.st_mtime < cutoff_time:
                                os.remove(file_path)
                                cleaned_count += 1
                                logging.getLogger(__name__).info(f"清理旧日志文件: {file_path}")
                        except Exception as e:
                            logging.getLogger(__name__).error(f"清理日志文件失败: {file_path}, 错误: {str(e)}")
                            
            return cleaned_count
            
        except Exception as e:
            logging.getLogger(__name__).error(f"清理旧日志失败: {str(e)}")
            return 0
            
    def get_plugin_logger_names(self) -> list:
        """获取所有插件日志记录器名称"""
        return list(self._plugin_loggers.keys())
        
    def clear_plugin_loggers(self):
        """清理所有插件日志记录器"""
        plugin_names = list(self._plugin_loggers.keys())
        
        for plugin_name in plugin_names:
            self.remove_plugin_logger(plugin_name)
            
        logging.getLogger(__name__).info("所有插件日志记录器已清理")
