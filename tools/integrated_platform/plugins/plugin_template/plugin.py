#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
插件模板 - 展示如何创建一个标准插件

这是一个完整的插件开发模板，包含了插件开发的所有基本要素：
- 插件基本信息定义
- 路由注册
- API接口定义
- 导航项配置
- 配置管理
"""

from typing import Dict, List, Any
from core.base_plugin import BasePlugin


class Plugin(BasePlugin):
    """插件模板类"""

    def __init__(self, **kwargs):
        # 先调用父类初始化
        super().__init__(**kwargs)
        # 然后设置插件信息
        self.name = "plugin_template"
        self.displayName = "插件模板"
        self.description = "这是一个插件开发模板，展示如何创建标准的插件模块"
        self.version = "1.0.0"
        self.author = "插件开发者"


    def getRoutes(self) -> List[Dict[str, Any]]:
        """获取页面路由配置"""
        return []

    def getApiRoutes(self) -> List[Dict[str, Any]]:
        """获取API路由配置"""
        return []

    def getNavItems(self) -> List[Dict[str, Any]]:
        """获取导航项配置"""
        return [
            {
                "title": "插件模板",
                "url": "/plugins/plugin_template/",
                "icon": "fas fa-puzzle-piece",
                "order": 100
            }
        ]

    def onInitialize(self):
        """插件初始化回调"""
        self.logger.info("插件模板初始化完成")

