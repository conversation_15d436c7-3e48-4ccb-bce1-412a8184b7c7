#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API管理器
"""

from typing import Dict, List, Any, Optional
import logging
from flask import Flask, jsonify, request
from functools import wraps


class APIManager:
    """API管理器"""
    
    def __init__(self, app: Flask = None):
        self.app = app
        self.logger = logging.getLogger('api_manager')
        
        # API存储
        self._plugin_apis: Dict[str, List[Dict[str, Any]]] = {}
        self._registered_apis: Dict[str, List[str]] = {}
        
        # 注册核心API
        if self.app:
            self._register_core_apis()
            
    def _register_core_apis(self):
        """注册核心API"""
        # 系统信息API
        @self.app.route('/api/system/info', methods=['GET'])
        def system_info():
            return jsonify({
                'success': True,
                'data': {
                    'name': '集成工具平台',
                    'version': '2.0.0',
                    'description': '基于插件架构的集成工具平台'
                }
            })
            
        # 插件列表API
        @self.app.route('/api/system/plugins', methods=['GET'])
        def list_plugins():
            try:
                plugin_manager = getattr(self.app, 'plugin_manager', None)

                if not plugin_manager:
                    return jsonify({
                        'success': False,
                        'message': '插件管理器未初始化'
                    }), 500
                    
                pluginsInfo = plugin_manager.getAllPluginInfo()
                return jsonify({
                    'success': True,
                    'data': pluginsInfo
                })
                
            except Exception as e:
                self.logger.error(f"获取插件列表失败: {str(e)}")
                return jsonify({
                    'success': False,
                    'message': str(e)
                }), 500

        self.logger.info("核心API注册完成")
        
    def register_plugin_apis(self, plugin_name: str, apis: List[Dict[str, Any]]) -> bool:
        """注册插件API"""
        try:
            if not self.app:
                self.logger.error("Flask应用未初始化")
                return False
                
            registered_endpoints = []
            
            for api_config in apis:
                endpoint = self._register_single_api(plugin_name, api_config)
                if endpoint:
                    registered_endpoints.append(endpoint)
                else:
                    # 如果有API注册失败，回滚已注册的API
                    self._unregister_api_endpoints(registered_endpoints)
                    return False
                    
            # 存储插件API信息
            self._plugin_apis[plugin_name] = apis
            self._registered_apis[plugin_name] = registered_endpoints
            
            self.logger.info(f"插件API注册成功: {plugin_name}, 注册了 {len(apis)} 个API")
            return True
            
        except Exception as e:
            self.logger.error(f"注册插件API失败: {plugin_name}, 错误: {str(e)}")
            return False
            
    def _register_single_api(self, plugin_name: str, api_config: Dict[str, Any]) -> Optional[str]:
        """注册单个API"""
        try:
            # 验证API配置
            if not self._validate_api_config(api_config):
                return None
                
            rule = api_config['rule']
            view_func = api_config['view_func']
            methods = api_config.get('methods', ['GET'])
            endpoint = api_config.get('endpoint')
            
            # 生成端点名称
            if not endpoint:
                endpoint = f"api_plugin_{plugin_name}_{view_func.__name__}"
            else:
                endpoint = f"api_plugin_{plugin_name}_{endpoint}"
                
            # 检查端点是否已存在
            if endpoint in self.app.view_functions:
                self.logger.error(f"API端点已存在: {endpoint}")
                return None
                
            # 添加API路由规则前缀
            if not rule.startswith('/'):
                rule = '/' + rule
            rule = f"/api/plugins/{plugin_name}{rule}"
            
            # 包装视图函数以添加统一的错误处理和响应格式
            wrapped_view_func = self._wrap_api_view_func(view_func, plugin_name)
            
            # 注册API路由
            self.app.add_url_rule(
                rule=rule,
                endpoint=endpoint,
                view_func=wrapped_view_func,
                methods=methods,
                **api_config.get('options', {})
            )
            
            self.logger.debug(f"API注册成功: {rule} -> {endpoint}")
            return endpoint
            
        except Exception as e:
            self.logger.error(f"注册单个API失败: {str(e)}")
            return None
            
    def _validate_api_config(self, api_config: Dict[str, Any]) -> bool:
        """验证API配置"""
        required_fields = ['rule', 'view_func']
        
        for field in required_fields:
            if field not in api_config:
                self.logger.error(f"API配置缺少必需字段: {field}")
                return False
                
        if not callable(api_config['view_func']):
            self.logger.error("view_func必须是可调用对象")
            return False
            
        return True
        
    def _wrap_api_view_func(self, view_func, plugin_name: str):
        """包装API视图函数"""
        @wraps(view_func)
        def wrapper(*args, **kwargs):
            try:
                # 调用原始视图函数
                result = view_func(*args, **kwargs)
                
                # 处理不同类型的返回值
                if isinstance(result, tuple):
                    # 如果返回元组，通常是 (response, status_code)
                    if len(result) == 2:
                        response, status_code = result
                        if hasattr(response, 'status_code'):
                            # 如果第一个元素是Response对象，直接返回
                            return response, status_code
                        else:
                            # 如果第一个元素不是Response对象，包装它
                            return jsonify(response), status_code
                    else:
                        # 其他情况，包装整个元组
                        return jsonify({
                            'success': True,
                            'data': result
                        })
                elif not hasattr(result, 'status_code'):
                    # 如果返回的不是Response对象，则包装为标准API响应
                    if isinstance(result, dict):
                        # 如果返回字典，检查是否已经是标准格式
                        if 'success' not in result:
                            result = {
                                'success': True,
                                'data': result
                            }
                        return jsonify(result)
                    else:
                        return jsonify({
                            'success': True,
                            'data': result
                        })

                return result
                
            except Exception as e:
                self.logger.error(f"插件API执行失败: {plugin_name}, 错误: {str(e)}")
                return jsonify({
                    'success': False,
                    'message': str(e),
                    'plugin': plugin_name
                }), 500
                
        return wrapper
        
    def unregister_plugin_apis(self, plugin_name: str) -> bool:
        """注销插件API"""
        try:
            if plugin_name not in self._registered_apis:
                self.logger.warning(f"插件API未注册: {plugin_name}")
                return True
                
            endpoints = self._registered_apis[plugin_name]
            self._unregister_api_endpoints(endpoints)
            
            # 清理存储的API信息
            if plugin_name in self._plugin_apis:
                del self._plugin_apis[plugin_name]
            if plugin_name in self._registered_apis:
                del self._registered_apis[plugin_name]
                
            self.logger.info(f"插件API注销成功: {plugin_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"注销插件API失败: {plugin_name}, 错误: {str(e)}")
            return False
            
    def _unregister_api_endpoints(self, endpoints: List[str]):
        """注销API端点"""
        for endpoint in endpoints:
            try:
                if endpoint in self.app.view_functions:
                    # 从视图函数中移除
                    del self.app.view_functions[endpoint]
                    
                    # 从URL映射中移除相关规则
                    rules_to_remove = []
                    for rule in self.app.url_map.iter_rules():
                        if rule.endpoint == endpoint:
                            rules_to_remove.append(rule)
                            
                    for rule in rules_to_remove:
                        self.app.url_map._rules.remove(rule)
                        if rule in self.app.url_map._rules_by_endpoint.get(endpoint, []):
                            self.app.url_map._rules_by_endpoint[endpoint].remove(rule)
                            
                    # 清理空的端点映射
                    if endpoint in self.app.url_map._rules_by_endpoint and not self.app.url_map._rules_by_endpoint[endpoint]:
                        del self.app.url_map._rules_by_endpoint[endpoint]
                        
                    self.logger.debug(f"API端点注销成功: {endpoint}")
                    
            except Exception as e:
                self.logger.error(f"注销API端点失败: {endpoint}, 错误: {str(e)}")
                
    def get_plugin_apis(self, plugin_name: str) -> List[Dict[str, Any]]:
        """获取插件API"""
        return self._plugin_apis.get(plugin_name, [])
        
    def get_all_plugin_apis(self) -> Dict[str, List[Dict[str, Any]]]:
        """获取所有插件API"""
        return self._plugin_apis.copy()
        
    def get_api_info(self) -> Dict[str, Any]:
        """获取API信息"""
        info = {
            'total_plugins': len(self._plugin_apis),
            'total_apis': sum(len(apis) for apis in self._plugin_apis.values()),
            'plugins': {}
        }
        
        for plugin_name, apis in self._plugin_apis.items():
            info['plugins'][plugin_name] = {
                'api_count': len(apis),
                'endpoints': self._registered_apis.get(plugin_name, []),
                'apis': [
                    {
                        'rule': api['rule'],
                        'methods': api.get('methods', ['GET']),
                        'endpoint': api.get('endpoint', 'auto-generated')
                    }
                    for api in apis
                ]
            }
            
        return info
        
    def list_all_apis(self) -> List[Dict[str, Any]]:
        """列出所有API"""
        apis = []
        
        for plugin_name, plugin_apis in self._plugin_apis.items():
            for api_config in plugin_apis:
                rule = api_config['rule']
                if not rule.startswith('/'):
                    rule = '/' + rule
                full_rule = f"/api/plugins/{plugin_name}{rule}"
                
                apis.append({
                    'plugin': plugin_name,
                    'rule': full_rule,
                    'methods': api_config.get('methods', ['GET']),
                    'endpoint': api_config.get('endpoint', 'auto-generated'),
                    'view_func': api_config['view_func'].__name__
                })
                
        return apis
        
    def clear_all_apis(self) -> bool:
        """清理所有插件API"""
        try:
            plugin_names = list(self._registered_apis.keys())
            
            for plugin_name in plugin_names:
                self.unregister_plugin_apis(plugin_name)
                
            self.logger.info("所有插件API清理完成")
            return True
            
        except Exception as e:
            self.logger.error(f"清理所有插件API失败: {str(e)}")
            return False
