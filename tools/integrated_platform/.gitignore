# =============================================================================
# 集成工具平台 .gitignore 配置
# =============================================================================

# -----------------------------------------------------------------------------
# Python 相关文件
# -----------------------------------------------------------------------------
# 字节码文件
__pycache__/
*.py[cod]
*$py.class

# 分发/打包
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# 虚拟环境
venv/
env/
ENV/
.venv/
.env/

# -----------------------------------------------------------------------------
# 日志文件
# -----------------------------------------------------------------------------
# 根目录日志
logs/
logs/*.log
*.log

# 插件日志文件
plugins/*/logs/
plugins/*/logs/*.log
plugins/*/logs/*.txt

# -----------------------------------------------------------------------------
# 插件生成的文件和目录
# -----------------------------------------------------------------------------
# 插件报告目录（包含pcap文件、CSV文件等）
plugins/*/reports/
plugins/*/reports/*
plugins/*/reports/*/*

# 插件任务数据文件
plugins/*/tasks.json
plugins/*/tasks.db

# 插件上传文件目录中的动态生成文件
plugins/*/uploads/*.pcap
plugins/*/uploads/*.csv
plugins/*/uploads/*.cache
plugins/*/uploads/*.conf
plugins/*/uploads/temp_*
plugins/*/uploads/tmp_*

# 插件临时文件
plugins/*/temp/
plugins/*/tmp/
plugins/*/*.tmp
plugins/*/*.temp

# -----------------------------------------------------------------------------
# 数据库文件
# -----------------------------------------------------------------------------
*.db
*.sqlite
*.sqlite3
dev.db
prod.db

# -----------------------------------------------------------------------------
# 系统和编辑器文件
# -----------------------------------------------------------------------------
# macOS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows
*.lnk

# Linux
*~

# IDE 文件
.vscode/
.idea/
*.swp
*.swo
*~

# -----------------------------------------------------------------------------
# 临时文件和缓存
# -----------------------------------------------------------------------------
# 通用临时文件
*.tmp
*.temp
*.bak
*.backup
*.orig

# 上传文件目录
uploads/
temp/
tmp/

# -----------------------------------------------------------------------------
# 配置文件（如果包含敏感信息）
# -----------------------------------------------------------------------------
# 注意：如果配置文件包含敏感信息，取消下面的注释
# config/production.py
# config/local.py
# .env
# .env.local

# -----------------------------------------------------------------------------
# 测试和覆盖率文件
# -----------------------------------------------------------------------------
.coverage
.pytest_cache/
htmlcov/
.tox/
.nox/
coverage.xml
*.cover
.hypothesis/

# -----------------------------------------------------------------------------
# 文档构建文件
# -----------------------------------------------------------------------------
docs/_build/
.readthedocs.yml

# -----------------------------------------------------------------------------
# 其他项目特定文件
# -----------------------------------------------------------------------------
# 压缩文件
*.zip
*.tar.gz
*.rar

# 大文件
*.pcap
*.cap

# 但保配置文件
!plugins/*/uploads/frag.conf
!plugins/*/uploads/*.conf