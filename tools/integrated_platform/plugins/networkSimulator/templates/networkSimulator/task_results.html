{% extends "base.html" %}

{% block title %}网络模拟访问 - 任务结果{% endblock %}

{% block content %}
<div class="network-simulator-container">
    <!-- 页面头部 -->
    <div class="page-header">
        <div class="header-content">
            <div class="header-title">
                <i class="fas fa-network-wired me-3"></i>
                <h1>网络模拟访问</h1>
            </div>
            <div class="header-actions">
                <a class="action-btn" href="/plugins/networkSimulator/">
                    <i class="fas fa-plus-circle"></i>
                    <span>新建任务</span>
                </a>
                <a class="action-btn active" href="/plugins/networkSimulator/task-results">
                    <i class="fas fa-list-alt"></i>
                    <span>任务结果</span>
                </a>
            </div>
        </div>
    </div>

    <!-- 任务列表面板 -->
    <div class="content-panels">
        <div class="panel results-panel">
            <div class="panel-header">
                <div class="panel-icon">
                    <i class="fas fa-list-alt"></i>
                </div>
                <div class="panel-title">
                    <h3>任务执行结果</h3>
                    <p>查看和管理网络模拟任务结果</p>
                </div>
                <div class="panel-actions">
                    <!-- 批量操作按钮组 -->
                    <div class="batch-actions" id="batchActions" style="display: none;">
                        <button class="btn btn-danger" onclick="batchDeleteTasks()">
                            <i class="fas fa-trash-alt"></i>
                            <span>批量删除</span>
                        </button>
                        <button class="btn btn-secondary" onclick="cancelBatchSelection()">
                            <i class="fas fa-times"></i>
                            <span>取消选择</span>
                        </button>
                    </div>
                    <button class="btn btn-secondary" onclick="refreshTasks()">
                        <i class="fas fa-sync-alt"></i>
                        <span>刷新</span>
                    </button>
                </div>
            </div>
            <div class="panel-body">
                <!-- 加载状态 -->
                <div class="loading-state" id="loadingState">
                    <div class="loading-spinner">
                        <i class="fas fa-spinner fa-spin"></i>
                    </div>
                    <div class="loading-text">正在加载任务列表...</div>
                </div>

                <!-- 空状态 -->
                <div class="empty-state" id="emptyState" style="display: none;">
                    <div class="empty-icon">
                        <i class="fas fa-inbox"></i>
                    </div>
                    <div class="empty-text">
                        <h4>暂无任务记录</h4>
                        <p>还没有执行过网络模拟任务</p>
                        <a href="/plugins/networkSimulator/create-task" class="btn btn-primary">
                            <i class="fas fa-plus"></i>
                            <span>创建第一个任务</span>
                        </a>
                    </div>
                </div>

                <!-- 任务列表 -->
                <div class="task-list" id="taskList" style="display: none;">
                    <div class="task-header">
                        <div class="task-col task-select">
                            <input type="checkbox" id="selectAllTasks" onchange="toggleSelectAll(this)">
                            <label for="selectAllTasks">全选</label>
                        </div>
                        <div class="task-col task-id">任务ID</div>
                        <div class="task-col task-name">任务名称</div>
                        <div class="task-col task-status">状态</div>
                        <div class="task-col task-time">创建时间</div>
                        <div class="task-col task-result">结果下载</div>
                        <div class="task-col task-actions">操作</div>
                    </div>
                    <div class="task-body" id="taskBody">
                        <!-- 任务项将通过JavaScript动态生成 -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 任务详情模态框 -->
<div class="modal" id="taskDetailModal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>任务详情</h3>
            <button class="modal-close" onclick="closeTaskDetail()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body">
            <div class="detail-section">
                <h4>基本信息</h4>
                <div class="detail-grid">
                    <div class="detail-item">
                        <div class="detail-label">任务ID：</div>
                        <div class="detail-value" id="detailTaskId">-</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">任务名称：</div>
                        <div class="detail-value" id="detailTaskName">-</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">任务状态：</div>
                        <div class="detail-value" id="detailTaskStatus">-</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">创建时间：</div>
                        <div class="detail-value" id="detailCreateTime">-</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">完成时间：</div>
                        <div class="detail-value" id="detailFinishTime">-</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">执行结果：</div>
                        <div class="detail-value" id="detailResult">-</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button class="btn btn-success" id="modalDownloadBtn" onclick="downloadTaskResult()">
                <i class="fas fa-download"></i>
                <span>下载抓包文件</span>
            </button>
            <button class="btn btn-secondary" onclick="closeTaskDetail()">
                <i class="fas fa-times"></i>
                <span>关闭</span>
            </button>
        </div>
    </div>
</div>

<!-- 确认删除模态框 -->
<div class="modal" id="deleteConfirmModal">
    <div class="modal-content modal-small">
        <div class="modal-header">
            <h3>确认删除</h3>
            <button class="modal-close" onclick="closeDeleteConfirm()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body">
            <div class="confirm-content">
                <div class="confirm-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="confirm-text">
                    <p>确定要删除这个任务吗？</p>
                    <p class="text-muted">删除后将无法恢复，包括相关的抓包文件。</p>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button class="btn btn-danger" id="confirmDeleteBtn" onclick="confirmDelete()">
                <i class="fas fa-trash"></i>
                <span>确认删除</span>
            </button>
            <button class="btn btn-secondary" onclick="closeDeleteConfirm()">
                <i class="fas fa-times"></i>
                <span>取消</span>
            </button>
        </div>
    </div>
</div>

<!-- 引入插件专用CSS -->
<link href="{{ url_for('static', filename='plugins/networkSimulator/css/style.css') }}" rel="stylesheet">
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='plugins/networkSimulator/js/task_results.js') }}"></script>
{% endblock %}
