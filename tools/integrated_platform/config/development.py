#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
开发环境配置
"""

import os
from .base import Config


class DevelopmentConfig(Config):
    """开发环境配置"""
    
    # 调试模式
    DEBUG = True
    TESTING = False
    
    # 数据库配置
    SQLALCHEMY_DATABASE_URI = os.environ.get('DEV_DATABASE_URL') or \
        'sqlite:///' + os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'dev.db')
    
    # 日志配置
    LOG_LEVEL = 'DEBUG'
    
    # 缓存配置（开发环境使用简单缓存）
    CACHE_TYPE = 'simple'
    CACHE_DEFAULT_TIMEOUT = 60  # 开发环境缓存时间短一些
    
    # 会话配置（开发环境不需要HTTPS）
    SESSION_COOKIE_SECURE = False
    
    # 安全配置（开发环境可以放宽一些限制）
    WTF_CSRF_ENABLED = False  # 开发环境可以禁用CSRF保护
    
    # API配置（开发环境限制宽松一些）
    API_RATE_LIMIT = '10000 per hour'
    
    # 插件配置
    PLUGIN_AUTO_LOAD = True
    PLUGIN_HOT_RELOAD = True  # 开发环境支持热重载
    
    # 开发工具配置
    FLASK_ENV = 'development'
    FLASK_DEBUG = True
    
    # 静态文件配置
    SEND_FILE_MAX_AGE_DEFAULT = 1  # 开发环境静态文件缓存时间短
    
    @staticmethod
    def init_app(app):
        """初始化开发环境配置"""
        Config.init_app(app)
        
        # 开发环境特定的初始化
        app.logger.info("开发环境配置初始化完成")
        
        # 如果是开发环境，可以添加一些调试工具
        if app.debug:
            # 可以在这里添加开发工具的初始化
            pass
