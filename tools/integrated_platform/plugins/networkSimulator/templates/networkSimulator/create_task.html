{% extends "base.html" %}

{% block title %}网络模拟访问 - 新建任务{% endblock %}

{% block content %}
<div class="network-simulator-container">
    <!-- 页面头部 -->
    <div class="page-header">
        <div class="header-content">
            <div class="header-title">
                <i class="fas fa-network-wired me-3"></i>
                <h1>网络模拟访问</h1>
            </div>
            <div class="header-actions">
                <a class="action-btn active" href="/plugins/networkSimulator/">
                    <i class="fas fa-plus-circle"></i>
                    <span>新建任务</span>
                </a>
                <a class="action-btn" href="/plugins/networkSimulator/task-results">
                    <i class="fas fa-list-alt"></i>
                    <span>任务结果</span>
                </a>
            </div>
        </div>
    </div>

    <!-- 任务创建面板 -->
    <div class="content-panels">
        <div class="panel task-panel">
            <div class="panel-header">
                <div class="panel-icon">
                    <i class="fas fa-plus-circle"></i>
                </div>
                <div class="panel-title">
                    <h3>新建网络模拟任务</h3>
                    <p>配置网络访问参数并启动模拟任务，支持HTTP/HTTPS/TCP/UDP/DNS协议的完整请求-响应交互</p>
                </div>
            </div>
            <div class="panel-body">
                <form id="createTaskForm" class="form-section">
                    <!-- 任务名称 -->
                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-tag"></i>
                            <span>任务名称</span>
                        </label>
                        <input type="text"
                               class="form-input"
                               id="taskName"
                               placeholder="请输入任务名称"
                               required>
                        <div class="form-hint">请输入一个有意义的任务名称，便于后续识别和管理</div>
                    </div>

                    <!-- 任务类型选择 -->
                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-tasks"></i>
                            <span>任务类型</span>
                        </label>
                        <div class="task-type-options">
                            <div class="task-type-option">
                                <input type="radio" id="targetTask" name="taskType" value="target" checked>
                                <label for="targetTask" class="task-type-label">
                                    <i class="fas fa-globe"></i>
                                    <span>目标地址访问</span>
                                    <small>模拟访问指定的IP地址或域名</small>
                                </label>
                            </div>
                            <div class="task-type-option">
                                <input type="radio" id="fileTask" name="taskType" value="file">
                                <label for="fileTask" class="task-type-label">
                                    <i class="fas fa-file-upload"></i>
                                    <span>文件传输模拟</span>
                                    <small>模拟HTTP文件上传传输</small>
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- 目标地址 -->
                    <div class="form-group" id="targetsGroup">
                        <label class="form-label">
                            <i class="fas fa-globe"></i>
                            <span>目标地址</span>
                        </label>
                        <textarea class="form-textarea"
                                  id="targets"
                                  rows="8"
                                  placeholder="请输入目标IP地址或域名，每行一个&#10;例如：&#10;***********&#10;www.baidu.com&#10;github.com&#10;*******"
                                  required></textarea>
                        <div class="form-hint">支持IP地址和域名，每行输入一个目标地址</div>
                    </div>

                    <!-- 文件上传 -->
                    <div class="form-group" id="filesGroup" style="display: none;">
                        <label class="form-label">
                            <i class="fas fa-file-upload"></i>
                            <span>上传文件</span>
                        </label>
                        <div class="file-upload-area">
                            <input type="file" id="files" class="form-input" multiple accept="*/*" style="display: none;">
                            <div class="file-drop-zone" onclick="document.getElementById('files').click()">
                                <i class="fas fa-cloud-upload-alt"></i>
                                <p>点击选择文件或拖拽文件到此处</p>
                                <small>支持多文件上传，每个文件将单独进行HTTP传输模拟</small>
                            </div>
                            <div id="fileList" class="file-list"></div>
                        </div>
                    </div>

                    <!-- 协议配置 -->
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-layer-group"></i>
                                <span>访问协议</span>
                            </label>
                            <select class="form-select" id="protocol" required>
                                <option value="http">HTTP</option>
                                <option value="https">HTTPS</option>
                                <option value="tcp">TCP</option>
                                <option value="udp">UDP</option>
                                <option value="dns">DNS</option>
                            </select>
                            <div class="form-hint">选择网络访问协议类型</div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-plug"></i>
                                <span>目标端口</span>
                            </label>
                            <input type="number"
                                   class="form-input"
                                   id="port"
                                   value="80"
                                   min="1"
                                   max="65535"
                                   required>
                            <div class="form-hint">目标服务端口号（1-65535）</div>
                        </div>
                    </div>

                    <!-- 提交按钮 -->
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary" id="submitBtn">
                            <i class="fas fa-play"></i>
                            <span>开始模拟</span>
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="resetForm()">
                            <i class="fas fa-undo"></i>
                            <span>重置表单</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- 任务状态面板 -->
        <div class="panel status-panel" id="statusPanel" style="display: none;">
            <div class="panel-header">
                <div class="panel-icon">
                    <i class="fas fa-tasks"></i>
                </div>
                <div class="panel-title">
                    <h3>任务执行状态</h3>
                    <p>实时显示任务执行进度和结果</p>
                </div>
            </div>
            <div class="panel-body">
                <div class="status-content">
                    <div class="status-item">
                        <div class="status-label">任务ID：</div>
                        <div class="status-value" id="taskId">-</div>
                    </div>
                    <div class="status-item">
                        <div class="status-label">任务状态：</div>
                        <div class="status-value" id="taskStatus">
                            <span class="status-badge status-pending">等待中</span>
                        </div>
                    </div>
                    <div class="status-item">
                        <div class="status-label">执行结果：</div>
                        <div class="status-value" id="taskResult">-</div>
                    </div>
                </div>
                
                <div class="progress-section">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="progress-text" id="progressText">准备中...</div>
                </div>

                <div class="action-buttons" id="actionButtons" style="display: none;">
                    <button class="btn btn-success" id="downloadBtn" onclick="downloadResult()">
                        <i class="fas fa-download"></i>
                        <span>下载抓包文件</span>
                    </button>
                    <button class="btn btn-info" onclick="viewResults()">
                        <i class="fas fa-eye"></i>
                        <span>查看结果</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 引入插件专用CSS -->
<link href="{{ url_for('static', filename='plugins/networkSimulator/css/style.css') }}" rel="stylesheet">
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='plugins/networkSimulator/js/create_task.js') }}"></script>
{% endblock %}
