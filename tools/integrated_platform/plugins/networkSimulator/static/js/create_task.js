/**
 * 网络模拟访问插件 - 创建任务页面脚本
 */

let currentTaskId = null;
let statusCheckInterval = null;

document.addEventListener('DOMContentLoaded', function() {
    // 初始化页面
    initializePage();
});

/**
 * 初始化页面
 */
function initializePage() {
    // 绑定表单提交事件
    bindFormEvents();

    // 绑定协议变化事件
    bindProtocolChange();

    // 绑定任务类型切换事件
    bindTaskTypeChange();

    // 绑定文件上传事件
    bindFileUploadEvents();

    // 添加表单验证
    addFormValidation();
}

/**
 * 绑定表单事件
 */
function bindFormEvents() {
    const form = document.getElementById('createTaskForm');
    if (form) {
        form.addEventListener('submit', handleFormSubmit);
    }
}

/**
 * 绑定协议变化事件
 */
function bindProtocolChange() {
    const protocolSelect = document.getElementById('protocol');
    const portInput = document.getElementById('port');
    
    if (protocolSelect && portInput) {
        protocolSelect.addEventListener('change', function() {
            const protocol = this.value;
            let defaultPort = 80;
            
            switch (protocol) {
                case 'http':
                    defaultPort = 80;
                    break;
                case 'https':
                    defaultPort = 443;
                    break;
                case 'tcp':
                    defaultPort = 80;
                    break;
                case 'udp':
                    defaultPort = 53;
                    break;
                case 'dns':
                    defaultPort = 53;
                    break;
            }
            
            portInput.value = defaultPort;
        });
    }
}

/**
 * 添加表单验证
 */
function addFormValidation() {
    const inputs = document.querySelectorAll('.form-input, .form-textarea, .form-select');
    
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            validateField(this);
        });
        
        input.addEventListener('input', function() {
            clearFieldError(this);
        });
    });
}

/**
 * 验证字段
 */
function validateField(field) {
    const value = field.value.trim();
    let isValid = true;
    let errorMessage = '';

    // 特殊处理文件输入框
    if (field.id === 'files') {
        const taskType = document.querySelector('input[name="taskType"]:checked').value;
        if (taskType === 'file') {
            if (selectedFiles.length === 0) {
                isValid = false;
                errorMessage = '请选择要上传的文件';
            }
        }
    } else if (field.hasAttribute('required') && !value) {
        isValid = false;
        errorMessage = '此字段为必填项';
    } else if (field.id === 'port') {
        const port = parseInt(value);
        if (isNaN(port) || port < 1 || port > 65535) {
            isValid = false;
            errorMessage = '端口号必须在1-65535之间';
        }
    } else if (field.id === 'targets') {
        const targets = value.split('\n').filter(t => t.trim());
        if (targets.length === 0) {
            isValid = false;
            errorMessage = '请至少输入一个目标地址';
        }
    }

    if (!isValid) {
        showFieldError(field, errorMessage);
    } else {
        clearFieldError(field);
    }

    return isValid;
}

/**
 * 显示字段错误
 */
function showFieldError(field, message) {
    clearFieldError(field);
    
    field.style.borderColor = '#e53e3e';
    
    const errorDiv = document.createElement('div');
    errorDiv.className = 'field-error';
    errorDiv.style.cssText = `
        color: #e53e3e;
        font-size: 0.85rem;
        margin-top: 5px;
    `;
    errorDiv.textContent = message;
    
    field.parentNode.appendChild(errorDiv);
}

/**
 * 清除字段错误
 */
function clearFieldError(field) {
    field.style.borderColor = '#e2e8f0';
    
    const errorDiv = field.parentNode.querySelector('.field-error');
    if (errorDiv) {
        errorDiv.remove();
    }
}

/**
 * 处理表单提交
 */
async function handleFormSubmit(event) {
    event.preventDefault();
    
    // 验证所有字段
    const form = event.target;
    const taskType = document.querySelector('input[name="taskType"]:checked').value;
    let isFormValid = true;

    // 根据任务类型验证不同的字段
    if (taskType === 'target') {
        // 目标地址任务验证
        const inputs = form.querySelectorAll('.form-input, .form-textarea, .form-select');
        inputs.forEach(input => {
            // 跳过文件输入框的验证
            if (input.id !== 'files' && !validateField(input)) {
                isFormValid = false;
            }
        });
    } else if (taskType === 'file') {
        // 文件传输任务验证
        const requiredInputs = ['taskName', 'protocol', 'port', 'files'];
        requiredInputs.forEach(inputId => {
            const input = document.getElementById(inputId);
            if (input && !validateField(input)) {
                isFormValid = false;
            }
        });
    }

    if (!isFormValid) {
        showAlert('请检查并修正表单中的错误', 'error');
        return;
    }
    
    // 收集表单数据 - 使用已声明的taskType变量

    if (taskType === 'target') {
        // 目标地址任务
        const formData = {
            taskName: document.getElementById('taskName').value.trim(),
            targets: document.getElementById('targets').value.trim(),
            protocol: document.getElementById('protocol').value,
            port: parseInt(document.getElementById('port').value),
            taskType: 'target'
        };

        // 提交任务
        await submitTask(formData);
    } else if (taskType === 'file') {
        // 文件传输任务
        if (selectedFiles.length === 0) {
            showAlert('请选择要上传的文件', 'error');
            return;
        }

        const formData = new FormData();
        formData.append('taskName', document.getElementById('taskName').value.trim());
        formData.append('protocol', document.getElementById('protocol').value);
        formData.append('port', parseInt(document.getElementById('port').value));
        formData.append('taskType', 'file');

        // 添加所有选中的文件
        selectedFiles.forEach((file, index) => {
            formData.append('files', file);
        });

        // 提交文件任务
        await submitFileTask(formData);
    }
}

/**
 * 提交文件任务
 */
async function submitFileTask(formData) {
    const submitBtn = document.getElementById('submitBtn');
    const originalText = submitBtn.innerHTML;

    try {
        // 禁用提交按钮
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i><span>上传中...</span>';

        const response = await fetch('/api/plugins/networkSimulator/file-tasks', {
            method: 'POST',
            body: formData  // 不设置Content-Type，让浏览器自动设置multipart/form-data
        });

        const result = await response.json();

        if (result.success) {
            currentTaskId = result.data.taskId;
            showTaskStatus();
            startStatusCheck();
            showAlert('文件传输任务创建成功，正在执行中...', 'success');
        } else {
            showAlert(result.message || '创建任务失败', 'error');
        }
    } catch (error) {
        console.error('提交文件任务失败:', error);
        showAlert('网络错误，请稍后重试', 'error');
    } finally {
        // 恢复提交按钮
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    }
}

/**
 * 提交任务
 */
async function submitTask(formData) {
    const submitBtn = document.getElementById('submitBtn');
    const originalText = submitBtn.innerHTML;
    
    try {
        // 禁用提交按钮
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i><span>创建中...</span>';
        
        const response = await fetch('/api/plugins/networkSimulator/tasks', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData)
        });
        
        const result = await response.json();
        
        if (result.success) {
            currentTaskId = result.data.taskId;
            showTaskStatus();
            startStatusCheck();
            showAlert('任务创建成功，正在执行中...', 'success');
        } else {
            throw new Error(result.message || '创建任务失败');
        }
        
    } catch (error) {
        console.error('提交任务失败:', error);
        showAlert(`创建任务失败: ${error.message}`, 'error');
    } finally {
        // 恢复提交按钮
        submitBtn.disabled = false;
        submitBtn.innerHTML = originalText;
    }
}

/**
 * 显示任务状态面板
 */
function showTaskStatus() {
    const statusPanel = document.getElementById('statusPanel');
    const taskIdElement = document.getElementById('taskId');
    
    if (statusPanel && taskIdElement) {
        taskIdElement.textContent = currentTaskId;
        statusPanel.style.display = 'block';
        
        // 滚动到状态面板
        statusPanel.scrollIntoView({ behavior: 'smooth' });
    }
}

/**
 * 开始状态检查
 */
function startStatusCheck() {
    if (statusCheckInterval) {
        clearInterval(statusCheckInterval);
    }
    
    statusCheckInterval = setInterval(checkTaskStatus, 2000);
}

/**
 * 检查任务状态
 */
async function checkTaskStatus() {
    if (!currentTaskId) return;
    
    try {
        const response = await fetch(`/api/plugins/networkSimulator/tasks/${currentTaskId}/status`);
        const result = await response.json();
        
        if (result.success) {
            updateTaskStatus(result.data);
            
            // 如果任务完成，停止检查
            if (result.data.status === 'completed' || result.data.status === 'failed') {
                clearInterval(statusCheckInterval);
                statusCheckInterval = null;
            }
        }
    } catch (error) {
        console.error('检查任务状态失败:', error);
    }
}

/**
 * 更新任务状态显示
 */
function updateTaskStatus(taskData) {
    const statusElement = document.getElementById('taskStatus');
    const resultElement = document.getElementById('taskResult');
    const progressFill = document.getElementById('progressFill');
    const progressText = document.getElementById('progressText');
    const actionButtons = document.getElementById('actionButtons');
    
    // 更新状态显示
    if (statusElement) {
        const statusClass = `status-${taskData.status}`;
        const statusText = getStatusText(taskData.status);
        statusElement.innerHTML = `<span class="status-badge ${statusClass}">${statusText}</span>`;
    }
    
    // 更新结果显示
    if (resultElement) {
        resultElement.textContent = taskData.result || '-';
    }
    
    // 更新进度条
    if (progressFill && progressText) {
        let progress = 0;
        let progressMessage = '';
        
        switch (taskData.status) {
            case 'pending':
                progress = 10;
                progressMessage = '任务排队中...';
                break;
            case 'running':
                progress = 50;
                progressMessage = '正在执行网络模拟...';
                break;
            case 'completed':
                progress = 100;
                progressMessage = '任务执行完成';
                break;
            case 'failed':
                progress = 100;
                progressMessage = '任务执行失败';
                break;
        }
        
        progressFill.style.width = `${progress}%`;
        progressText.textContent = progressMessage;
    }
    
    // 显示操作按钮
    if (actionButtons && (taskData.status === 'completed' || taskData.status === 'failed')) {
        actionButtons.style.display = 'flex';

        const downloadBtn = document.getElementById('downloadBtn');
        if (downloadBtn) {
            downloadBtn.style.display = taskData.hasReport ? 'inline-flex' : 'none';
        }

        // 任务完成后3秒自动跳转到任务结果页面
        if (taskData.status === 'completed') {
            setTimeout(() => {
                showAlert('任务执行完成，即将跳转到任务结果页面...', 'success');
                setTimeout(() => {
                    window.location.href = '/plugins/networkSimulator/task-results';
                }, 1500);
            }, 3000);
        }
    }
}

/**
 * 获取状态文本
 */
function getStatusText(status) {
    const statusMap = {
        'pending': '等待中',
        'running': '执行中',
        'completed': '已完成',
        'failed': '失败'
    };
    
    return statusMap[status] || status;
}

/**
 * 下载结果文件
 */
function downloadResult() {
    if (currentTaskId) {
        window.open(`/api/plugins/networkSimulator/tasks/${currentTaskId}/download-report`, '_blank');
    }
}

/**
 * 查看结果
 */
function viewResults() {
    window.location.href = '/plugins/networkSimulator/task-results';
}

/**
 * 重置表单
 */
function resetForm() {
    const form = document.getElementById('createTaskForm');
    if (form) {
        form.reset();
        
        // 清除所有错误提示
        const errors = form.querySelectorAll('.field-error');
        errors.forEach(error => error.remove());
        
        // 重置字段样式
        const inputs = form.querySelectorAll('.form-input, .form-textarea, .form-select');
        inputs.forEach(input => {
            input.style.borderColor = '#e2e8f0';
        });
        
        // 隐藏状态面板
        const statusPanel = document.getElementById('statusPanel');
        if (statusPanel) {
            statusPanel.style.display = 'none';
        }
        
        // 清除状态检查
        if (statusCheckInterval) {
            clearInterval(statusCheckInterval);
            statusCheckInterval = null;
        }
        
        currentTaskId = null;
    }
}

/**
 * 显示提示信息
 */
function showAlert(message, type = 'info') {
    // 创建提示框
    const alert = document.createElement('div');
    alert.className = `alert alert-${type}`;
    alert.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 8px;
        color: white;
        font-weight: 600;
        z-index: 1001;
        max-width: 400px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        transform: translateX(100%);
        transition: transform 0.3s ease;
    `;
    
    // 设置背景色
    const colors = {
        'success': '#48bb78',
        'error': '#f56565',
        'warning': '#ed8936',
        'info': '#4299e1'
    };
    alert.style.background = colors[type] || colors.info;
    
    alert.textContent = message;
    
    document.body.appendChild(alert);
    
    // 显示动画
    setTimeout(() => {
        alert.style.transform = 'translateX(0)';
    }, 100);
    
    // 自动隐藏
    setTimeout(() => {
        alert.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (alert.parentNode) {
                alert.parentNode.removeChild(alert);
            }
        }, 300);
    }, 3000);
}

/**
 * 绑定任务类型切换事件
 */
function bindTaskTypeChange() {
    const taskTypeRadios = document.querySelectorAll('input[name="taskType"]');
    const targetsGroup = document.getElementById('targetsGroup');
    const filesGroup = document.getElementById('filesGroup');
    const protocolSelect = document.getElementById('protocol');
    const portInput = document.getElementById('port');

    // 直接绑定每个 radio 按钮的 change 事件
    taskTypeRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            handleTaskTypeChange(this.value);
        });
    });

    // 同时绑定 label 的点击事件
    const targetLabel = document.querySelector('label[for="targetTask"]');
    const fileLabel = document.querySelector('label[for="fileTask"]');

    if (targetLabel) {
        targetLabel.addEventListener('click', function() {
            document.getElementById('targetTask').checked = true;
            handleTaskTypeChange('target');
        });
    }

    if (fileLabel) {
        fileLabel.addEventListener('click', function() {
            document.getElementById('fileTask').checked = true;
            handleTaskTypeChange('file');
        });
    }

    // 提取任务类型切换处理逻辑
    function handleTaskTypeChange(taskType) {
        if (taskType === 'target') {
            // 显示目标地址输入
            targetsGroup.style.display = 'block';
            filesGroup.style.display = 'none';
            document.getElementById('targets').required = true;
            document.getElementById('files').required = false;

            // 清除文件相关的错误提示
            clearFieldError(document.getElementById('files'));

            // 恢复所有协议选项
            protocolSelect.innerHTML = `
                <option value="http">HTTP</option>
                <option value="https">HTTPS</option>
                <option value="tcp">TCP</option>
                <option value="udp">UDP</option>
                <option value="dns">DNS</option>
            `;
        } else if (taskType === 'file') {
            // 显示文件上传
            targetsGroup.style.display = 'none';
            filesGroup.style.display = 'block';
            document.getElementById('targets').required = false;
            document.getElementById('files').required = true;

            // 清除目标地址相关的错误提示
            clearFieldError(document.getElementById('targets'));

            // 文件传输只支持 HTTP 协议
            protocolSelect.innerHTML = `
                <option value="http">HTTP</option>
            `;
            protocolSelect.value = 'http';
            portInput.value = 80;
        }
    }

    // 设置初始状态
    const checkedRadio = document.querySelector('input[name="taskType"]:checked');
    if (checkedRadio) {
        handleTaskTypeChange(checkedRadio.value);
    }

    // 添加事件委托处理任务类型标签点击
    const taskTypeOptions = document.querySelector('.task-type-options');
    if (taskTypeOptions) {
        taskTypeOptions.addEventListener('click', function(e) {
            const label = e.target.closest('.task-type-label');
            if (label) {
                const radio = document.getElementById(label.getAttribute('for'));
                if (radio) {
                    radio.checked = true;
                    handleTaskTypeChange(radio.value);
                }
            }
        });
    }
}

let selectedFiles = [];

/**
 * 绑定文件上传事件
 */
function bindFileUploadEvents() {
    const filesInput = document.getElementById('files');
    const fileDropZone = document.querySelector('.file-drop-zone');
    const fileList = document.getElementById('fileList');

    if (!filesInput || !fileDropZone || !fileList) return;

    // 文件选择事件
    filesInput.addEventListener('change', handleFileSelect);

    // 拖拽事件
    fileDropZone.addEventListener('dragover', handleDragOver);
    fileDropZone.addEventListener('drop', handleFileDrop);
    fileDropZone.addEventListener('dragleave', handleDragLeave);
}

/**
 * 处理文件选择
 */
function handleFileSelect(event) {
    const files = Array.from(event.target.files);
    addFilesToList(files);
}

/**
 * 处理拖拽悬停
 */
function handleDragOver(event) {
    event.preventDefault();
    event.currentTarget.style.borderColor = '#3b82f6';
    event.currentTarget.style.background = '#eff6ff';
}

/**
 * 处理拖拽离开
 */
function handleDragLeave(event) {
    event.currentTarget.style.borderColor = '#cbd5e0';
    event.currentTarget.style.background = '#f7fafc';
}

/**
 * 处理文件拖拽放置
 */
function handleFileDrop(event) {
    event.preventDefault();
    event.currentTarget.style.borderColor = '#cbd5e0';
    event.currentTarget.style.background = '#f7fafc';

    const files = Array.from(event.dataTransfer.files);
    addFilesToList(files);
}

/**
 * 添加文件到列表
 */
function addFilesToList(files) {
    files.forEach(file => {
        // 检查文件是否已存在
        if (!selectedFiles.find(f => f.name === file.name && f.size === file.size)) {
            selectedFiles.push(file);
        }
    });

    renderFileList();

    // 更新文件输入框的验证状态
    const filesInput = document.getElementById('files');
    if (filesInput) {
        clearFieldError(filesInput);
    }
}

/**
 * 渲染文件列表
 */
function renderFileList() {
    const fileList = document.getElementById('fileList');
    if (!fileList) return;

    fileList.innerHTML = '';

    selectedFiles.forEach((file, index) => {
        const fileItem = document.createElement('div');
        fileItem.className = 'file-item';

        const fileSize = formatFileSize(file.size);
        const fileExtension = getFileExtension(file.name);

        fileItem.innerHTML = `
            <div class="file-info">
                <div class="file-icon">
                    <i class="fas fa-file"></i>
                </div>
                <div class="file-details">
                    <h4>${file.name}</h4>
                    <p>${fileSize} • ${fileExtension.toUpperCase()}</p>
                </div>
            </div>
            <button type="button" class="file-remove" onclick="removeFile(${index})">
                <i class="fas fa-times"></i>
            </button>
        `;

        fileList.appendChild(fileItem);
    });
}

/**
 * 移除文件
 */
function removeFile(index) {
    selectedFiles.splice(index, 1);
    renderFileList();

    // 如果没有文件了，重新验证文件输入框
    if (selectedFiles.length === 0) {
        const filesInput = document.getElementById('files');
        if (filesInput) {
            validateField(filesInput);
        }
    }
}

/**
 * 格式化文件大小
 */
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 获取文件扩展名
 */
function getFileExtension(filename) {
    return filename.split('.').pop() || 'file';
}
