#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生产环境配置
"""

import os
from .base import Config


class ProductionConfig(Config):
    """生产环境配置"""
    
    # 调试模式
    DEBUG = False
    TESTING = False
    
    # 数据库配置
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or \
        'sqlite:///' + os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'prod.db')
    
    # 日志配置
    LOG_LEVEL = os.environ.get('LOG_LEVEL') or 'INFO'
    
    # 缓存配置（生产环境使用Redis）
    CACHE_TYPE = os.environ.get('CACHE_TYPE') or 'redis'
    CACHE_REDIS_URL = os.environ.get('REDIS_URL') or 'redis://localhost:6379/0'
    CACHE_DEFAULT_TIMEOUT = 300
    
    # 会话配置（生产环境需要HTTPS）
    SESSION_COOKIE_SECURE = True
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Strict'
    
    # 安全配置
    WTF_CSRF_ENABLED = True
    WTF_CSRF_TIME_LIMIT = 3600
    
    # API配置
    API_RATE_LIMIT = os.environ.get('API_RATE_LIMIT') or '1000 per hour'
    API_RATE_LIMIT_STORAGE_URL = os.environ.get('REDIS_URL') or 'redis://localhost:6379/1'
    
    # 插件配置
    PLUGIN_AUTO_LOAD = True
    PLUGIN_HOT_RELOAD = False  # 生产环境不支持热重载
    
    # 生产环境配置
    FLASK_ENV = 'production'
    FLASK_DEBUG = False
    
    # 静态文件配置
    SEND_FILE_MAX_AGE_DEFAULT = 31536000  # 1年
    
    # 错误处理配置
    PROPAGATE_EXCEPTIONS = False
    
    # 数据库连接池配置
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_pre_ping': True,
        'pool_recycle': 300,
        'pool_size': 10,
        'max_overflow': 20,
    }
    
    @staticmethod
    def init_app(app):
        """初始化生产环境配置"""
        Config.init_app(app)
        
        # 生产环境特定的初始化
        app.logger.info("生产环境配置初始化完成")
        
        # 生产环境错误处理
        import logging
        from logging.handlers import SMTPHandler, RotatingFileHandler
        
        # 邮件错误处理器（如果配置了邮件服务）
        mail_server = os.environ.get('MAIL_SERVER')
        if mail_server:
            mail_port = int(os.environ.get('MAIL_PORT') or 587)
            mail_use_tls = os.environ.get('MAIL_USE_TLS', 'true').lower() in ['true', 'on', '1']
            mail_username = os.environ.get('MAIL_USERNAME')
            mail_password = os.environ.get('MAIL_PASSWORD')
            
            auth = None
            if mail_username or mail_password:
                auth = (mail_username, mail_password)
                
            secure = None
            if mail_use_tls:
                secure = ()
                
            mail_handler = SMTPHandler(
                mailhost=(mail_server, mail_port),
                fromaddr=os.environ.get('MAIL_FROM', '<EMAIL>'),
                toaddrs=os.environ.get('ADMINS', '').split(','),
                subject='集成工具平台错误',
                credentials=auth,
                secure=secure
            )
            mail_handler.setLevel(logging.ERROR)
            app.logger.addHandler(mail_handler)
            
        # 文件错误处理器
        if not os.path.exists('logs'):
            os.mkdir('logs')
            
        file_handler = RotatingFileHandler(
            'logs/app.log',
            maxBytes=10240000,  # 10MB
            backupCount=10
        )
        file_handler.setFormatter(logging.Formatter(
            '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
        ))
        file_handler.setLevel(logging.INFO)
        app.logger.addHandler(file_handler)
        
        app.logger.setLevel(logging.INFO)
        app.logger.info('集成工具平台启动')
