#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终验证文件上传修复效果
"""

import os
import sys
import tempfile
import logging
import time
import json

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from plugins.networkSimulator.network_simulator import NetworkSimulator

def setupLogger():
    """设置日志"""
    logger = logging.getLogger('final_verification')
    logger.setLevel(logging.INFO)
    
    handler = logging.StreamHandler()
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    
    return logger

def createTestFile(content: str) -> str:
    """创建测试文件"""
    with tempfile.NamedTemporaryFile(mode='w', suffix='_verification.txt', delete=False, encoding='utf-8') as f:
        f.write(content)
        return f.name

def verifyFileUpload():
    """验证文件上传功能"""
    logger = setupLogger()
    
    print("=" * 80)
    print("文件上传功能修复验证")
    print("=" * 80)
    
    # 创建网络模拟器实例
    plugin_dir = os.path.dirname(os.path.abspath(__file__))
    simulator = NetworkSimulator(plugin_dir, logger)
    
    # 创建包含特殊标识的测试文件
    test_content = """FILE_UPLOAD_VERIFICATION_TEST
这是用于验证文件上传修复效果的测试文件。

文件内容包含：
1. 中文字符：测试文件上传功能
2. 英文字符：File upload test content
3. 特殊字符：!@#$%^&*()_+-=[]{}|;:,.<>?
4. 数字：1234567890

测试目标：
- 验证文件内容能够完整传输
- 确认multipart/form-data格式正确
- 检查网络流量包含文件数据

""" + "=" * 50 + "\n"
    test_content += "重复内容行，用于增加文件大小\n" * 20
    
    test_file_path = createTestFile(test_content)
    
    try:
        # 验证文件创建成功
        with open(test_file_path, 'r', encoding='utf-8') as f:
            actual_content = f.read()
        
        file_size = len(actual_content.encode('utf-8'))
        
        print(f"✓ 测试文件创建成功")
        print(f"  路径: {test_file_path}")
        print(f"  大小: {file_size} 字节")
        print(f"  内容预览: {actual_content[:100]}...")
        
        # 准备文件信息
        files = [{
            'name': 'verification_test.txt',
            'path': test_file_path,
            'size': file_size
        }]
        
        # 创建输出目录
        output_dir = tempfile.mkdtemp(prefix='verification_test_')
        print(f"✓ 输出目录: {output_dir}")
        
        print("\n" + "-" * 60)
        print("开始执行文件传输模拟...")
        print("-" * 60)
        
        # 执行文件传输模拟
        result = simulator.simulateFileTransfer(
            files=files,
            protocol='http',
            port=8888,
            output_dir=output_dir,
            target_ip="**********",
            target_domain="verification.testserver.com"
        )
        
        print("\n" + "=" * 60)
        print("验证结果:")
        print("=" * 60)
        
        # 分析结果
        success = result.get('success', False)
        message = result.get('message', '')
        details = result.get('details', [])
        
        print(f"总体状态: {'✓ 成功' if success else '✗ 失败'}")
        print(f"消息: {message}")
        
        if details:
            for i, detail in enumerate(details, 1):
                print(f"\n文件 {i} 详细信息:")
                print(f"  文件名: {detail.get('file', 'unknown')}")
                print(f"  状态: {'✓ 成功' if detail.get('success', False) else '✗ 失败'}")
                print(f"  消息: {detail.get('message', '')}")
                
                if 'details' in detail:
                    file_details = detail['details']
                    print(f"  详细信息:")
                    for key, value in file_details.items():
                        print(f"    {key}: {value}")
        
        # 检查关键指标
        verification_points = []
        
        # 1. 检查是否成功传输
        if success:
            verification_points.append("✓ 文件传输模拟执行成功")
        else:
            verification_points.append("✗ 文件传输模拟执行失败")
        
        # 2. 检查是否有详细信息
        if details and len(details) > 0:
            verification_points.append("✓ 包含文件传输详细信息")
            
            # 3. 检查文件大小信息
            first_detail = details[0]
            if 'details' in first_detail and 'size' in first_detail['details']:
                reported_size = first_detail['details']['size']
                if reported_size == file_size:
                    verification_points.append(f"✓ 文件大小正确 ({reported_size} 字节)")
                else:
                    verification_points.append(f"✗ 文件大小不匹配 (期望: {file_size}, 实际: {reported_size})")
            
            # 4. 检查响应状态码
            if 'details' in first_detail and 'status_code' in first_detail['details']:
                status_code = first_detail['details']['status_code']
                if status_code == 200:
                    verification_points.append(f"✓ HTTP响应状态码正确 ({status_code})")
                else:
                    verification_points.append(f"? HTTP响应状态码: {status_code}")
        else:
            verification_points.append("✗ 缺少文件传输详细信息")
        
        print("\n" + "-" * 60)
        print("验证要点:")
        print("-" * 60)
        for point in verification_points:
            print(point)
        
        # 总结
        success_count = sum(1 for p in verification_points if p.startswith("✓"))
        total_count = len(verification_points)
        
        print("\n" + "=" * 60)
        print(f"验证总结: {success_count}/{total_count} 项通过")
        
        if success_count == total_count:
            print("🎉 所有验证项目通过！文件上传功能修复成功！")
            return True
        elif success_count >= total_count * 0.8:
            print("⚠️  大部分验证项目通过，文件上传功能基本正常")
            return True
        else:
            print("❌ 多个验证项目失败，文件上传功能仍有问题")
            return False
        
    finally:
        # 清理测试文件
        try:
            os.unlink(test_file_path)
        except:
            pass

if __name__ == '__main__':
    success = verifyFileUpload()
    sys.exit(0 if success else 1)
