{% extends "base.html" %}

{% block title %}{{ plugin_info.display_name }} 设置 - {{ app_name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2>
                        <i class="fas fa-cog me-2"></i>{{ plugin_info.display_name }} 设置
                    </h2>
                    <p class="text-muted mb-0">配置插件参数和选项</p>
                </div>
                <div>
                    <a href="/plugins/plugin_template/" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-1"></i>返回
                    </a>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 配置表单 -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-sliders-h me-2"></i>配置选项
                    </h5>
                </div>
                <div class="card-body">
                    <form id="settingsForm">
                        {% for key, schema_info in schema.items() %}
                        <div class="mb-3">
                            <label for="{{ key }}" class="form-label">
                                {{ schema_info.description }}
                                {% if schema_info.get('required', False) %}
                                <span class="text-danger">*</span>
                                {% endif %}
                            </label>
                            
                            {% if schema_info.type == 'boolean' %}
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="{{ key }}" name="{{ key }}" 
                                       {% if config.get(key, schema_info.get('default', False)) %}checked{% endif %}>
                                <label class="form-check-label" for="{{ key }}">
                                    启用此选项
                                </label>
                            </div>
                            
                            {% elif schema_info.type == 'integer' %}
                            <input type="number" class="form-control" id="{{ key }}" name="{{ key }}" 
                                   value="{{ config.get(key, schema_info.get('default', '')) }}"
                                   {% if schema_info.get('min') %}min="{{ schema_info.min }}"{% endif %}
                                   {% if schema_info.get('max') %}max="{{ schema_info.max }}"{% endif %}
                                   {% if schema_info.get('required', False) %}required{% endif %}>
                            
                            {% elif schema_info.get('choices') %}
                            <select class="form-select" id="{{ key }}" name="{{ key }}" 
                                    {% if schema_info.get('required', False) %}required{% endif %}>
                                {% for choice in schema_info.choices %}
                                <option value="{{ choice }}" 
                                        {% if config.get(key, schema_info.get('default')) == choice %}selected{% endif %}>
                                    {{ choice }}
                                </option>
                                {% endfor %}
                            </select>
                            
                            {% else %}
                            <input type="text" class="form-control" id="{{ key }}" name="{{ key }}" 
                                   value="{{ config.get(key, schema_info.get('default', '')) }}"
                                   {% if schema_info.get('required', False) %}required{% endif %}>
                            {% endif %}
                            
                            {% if schema_info.get('help') %}
                            <div class="form-text">{{ schema_info.help }}</div>
                            {% endif %}
                        </div>
                        {% endfor %}
                        
                        <div class="d-flex justify-content-between">
                            <button type="button" class="btn btn-outline-secondary" onclick="resetForm()">
                                <i class="fas fa-undo me-1"></i>重置
                            </button>
                            <div>
                                <button type="button" class="btn btn-outline-primary me-2" onclick="testConfig()">
                                    <i class="fas fa-vial me-1"></i>测试配置
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save me-1"></i>保存配置
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- 配置说明 -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>配置说明
                    </h5>
                </div>
                <div class="card-body">
                    <h6>配置项说明：</h6>
                    <ul class="list-unstyled">
                        {% for key, schema_info in schema.items() %}
                        <li class="mb-2">
                            <strong>{{ schema_info.description }}</strong>
                            <br>
                            <small class="text-muted">
                                类型: {{ schema_info.type }}
                                {% if schema_info.get('default') is not none %}
                                | 默认值: {{ schema_info.default }}
                                {% endif %}
                                {% if schema_info.get('required', False) %}
                                | <span class="text-danger">必需</span>
                                {% endif %}
                            </small>
                        </li>
                        {% endfor %}
                    </ul>
                </div>
            </div>

            <!-- 当前配置 -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-eye me-2"></i>当前配置
                    </h5>
                </div>
                <div class="card-body">
                    <pre class="bg-light p-2 rounded"><code>{{ config | tojson(indent=2) }}</code></pre>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 测试结果模态框 -->
<div class="modal fade" id="testModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">配置测试结果</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="test-result"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 表单提交处理
document.getElementById('settingsForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const config = {};
    
    // 处理表单数据
    for (let [key, value] of formData.entries()) {
        const input = document.getElementById(key);
        
        if (input.type === 'checkbox') {
            config[key] = input.checked;
        } else if (input.type === 'number') {
            config[key] = parseInt(value) || 0;
        } else {
            config[key] = value;
        }
    }
    
    // 处理未选中的复选框
    document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
        if (!formData.has(checkbox.name)) {
            config[checkbox.name] = false;
        }
    });
    
    // 提交配置
    fetch('/plugins/plugin_template/settings', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(config)
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            alert('配置保存成功！');
            location.reload();
        } else {
            alert('配置保存失败: ' + result.message);
        }
    })
    .catch(error => {
        console.error('保存配置失败:', error);
        alert('保存配置失败: ' + error.message);
    });
});

// 重置表单
function resetForm() {
    if (confirm('确定要重置所有配置到默认值吗？')) {
        document.getElementById('settingsForm').reset();
        
        // 重置复选框到默认状态
        {% for key, schema_info in schema.items() %}
        {% if schema_info.type == 'boolean' %}
        document.getElementById('{{ key }}').checked = {{ 'true' if schema_info.get('default', False) else 'false' }};
        {% endif %}
        {% endfor %}
    }
}

// 测试配置
function testConfig() {
    const formData = new FormData(document.getElementById('settingsForm'));
    const config = {};
    
    // 处理表单数据
    for (let [key, value] of formData.entries()) {
        const input = document.getElementById(key);
        
        if (input.type === 'checkbox') {
            config[key] = input.checked;
        } else if (input.type === 'number') {
            config[key] = parseInt(value) || 0;
        } else {
            config[key] = value;
        }
    }
    
    // 处理未选中的复选框
    document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
        if (!formData.has(checkbox.name)) {
            config[checkbox.name] = false;
        }
    });
    
    // 测试配置
    fetch('/api/plugins/plugin_template/process', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({test_config: config})
    })
    .then(response => response.json())
    .then(result => {
        document.getElementById('test-result').innerHTML = `
            <div class="alert alert-${result.success ? 'success' : 'danger'}">
                <h6>${result.success ? '配置测试成功' : '配置测试失败'}</h6>
                <p>${result.message}</p>
            </div>
            <pre class="bg-light p-2 rounded"><code>${JSON.stringify(result, null, 2)}</code></pre>
        `;
        
        // 显示模态框
        new bootstrap.Modal(document.getElementById('testModal')).show();
    })
    .catch(error => {
        console.error('测试配置失败:', error);
        document.getElementById('test-result').innerHTML = `
            <div class="alert alert-danger">
                <h6>测试失败</h6>
                <p>${error.message}</p>
            </div>
        `;
        
        new bootstrap.Modal(document.getElementById('testModal')).show();
    });
}
</script>
{% endblock %}
