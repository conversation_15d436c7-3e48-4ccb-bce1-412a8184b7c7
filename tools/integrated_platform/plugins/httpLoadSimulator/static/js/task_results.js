/**
 * HTTP负载模拟访问插件 - 任务结果页面脚本
 */

let tasks = [];
let currentTaskId = null;

document.addEventListener('DOMContentLoaded', function() {
    console.log('HTTP负载模拟任务结果页面已加载');
    
    // 初始化页面
    initializePage();
});

/**
 * 初始化页面
 */
function initializePage() {
    // 加载任务列表
    loadTasks();
    
    // 绑定事件监听器
    bindEventListeners();
}

/**
 * 绑定事件监听器
 */
function bindEventListeners() {
    // 绑定模态框关闭事件
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('modal')) {
            closeAllModals();
        }
    });
    
    // 绑定ESC键关闭模态框
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeAllModals();
        }
    });
}

/**
 * 加载任务列表
 */
async function loadTasks() {
    const loadingState = document.getElementById('loadingState');
    const emptyState = document.getElementById('emptyState');
    const taskList = document.getElementById('taskList');
    
    try {
        // 显示加载状态
        showElement(loadingState);
        hideElement(emptyState);
        hideElement(taskList);
        
        const response = await fetch('/api/plugins/httpLoadSimulator/tasks');
        const result = await response.json();
        
        if (result.success) {
            tasks = result.data || [];
            
            if (tasks.length === 0) {
                // 显示空状态
                hideElement(loadingState);
                showElement(emptyState);
                hideElement(taskList);
            } else {
                // 显示任务列表
                hideElement(loadingState);
                hideElement(emptyState);
                showElement(taskList);
                renderTaskList();
            }
        } else {
            throw new Error(result.message || '加载任务列表失败');
        }
        
    } catch (error) {
        console.error('加载任务列表失败:', error);
        hideElement(loadingState);
        showAlert(`加载任务列表失败: ${error.message}`, 'error');
    }
}

/**
 * 渲染任务列表
 */
function renderTaskList() {
    const taskBody = document.getElementById('taskBody');
    if (!taskBody) return;
    
    taskBody.innerHTML = '';
    
    tasks.forEach(task => {
        const taskRow = createTaskRow(task);
        taskBody.appendChild(taskRow);
    });
}

/**
 * 创建任务行
 */
function createTaskRow(task) {
    const row = document.createElement('div');
    row.className = 'task-row';
    
    // 格式化时间
    const createTime = formatDateTime(task.createTime);
    
    // 获取状态显示
    const statusBadge = getStatusBadge(task.status);
    
    row.innerHTML = `
        <div class="task-col task-select">
            <input type="checkbox" class="task-checkbox" value="${task.id}" onchange="updateBatchActions()">
        </div>
        <div class="task-col task-id">${task.id}</div>
        <div class="task-col task-name">${escapeHtml(task.name)}</div>
        <div class="task-col">${statusBadge}</div>
        <div class="task-col task-time">${createTime}</div>
        <div class="task-col task-result">
            ${task.status === 'completed' && task.hasReport ? `
                <button class="btn btn-sm btn-success" onclick="downloadTaskReport('${task.id}')" title="下载报文压缩包">
                    <i class="fas fa-download"></i> 下载报文
                </button>
            ` : task.status === 'completed' ? `
                <span class="text-muted">无报文文件</span>
            ` : task.status === 'failed' ? `
                <span class="text-danger">执行失败</span>
            ` : `
                <span class="text-muted">-</span>
            `}
        </div>
        <div class="task-col task-actions">
            <button class="btn btn-sm btn-info" onclick="showTaskDetail('${task.id}')" title="查看详情">
                <i class="fas fa-eye"></i>
            </button>
            <button class="btn btn-sm btn-danger" onclick="showDeleteConfirm('${task.id}')" title="删除任务">
                <i class="fas fa-trash"></i>
            </button>
        </div>
    `;
    
    return row;
}

/**
 * 格式化日期时间
 */
function formatDateTime(dateTimeStr) {
    if (!dateTimeStr) return '-';
    
    try {
        const date = new Date(dateTimeStr);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    } catch (e) {
        return dateTimeStr;
    }
}

/**
 * 获取状态徽章
 */
function getStatusBadge(status) {
    const statusMap = {
        'pending': { text: '等待中', class: 'status-pending' },
        'running': { text: '执行中', class: 'status-running' },
        'completed': { text: '已完成', class: 'status-completed' },
        'failed': { text: '失败', class: 'status-failed' }
    };
    
    const statusInfo = statusMap[status] || { text: status, class: 'status-pending' };
    return `<span class="status-badge ${statusInfo.class}">${statusInfo.text}</span>`;
}

/**
 * HTML转义
 */
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

/**
 * 显示元素
 */
function showElement(element) {
    if (element) {
        element.style.display = 'block';
    }
}

/**
 * 隐藏元素
 */
function hideElement(element) {
    if (element) {
        element.style.display = 'none';
    }
}

/**
 * 显示任务详情
 */
function showTaskDetail(taskId) {
    const task = tasks.find(t => t.id === taskId);
    if (!task) return;
    
    currentTaskId = taskId;
    
    // 填充模态框内容
    document.getElementById('modalTaskId').textContent = task.id;
    document.getElementById('modalTaskName').textContent = task.name;
    document.getElementById('modalCreateTime').textContent = formatDateTime(task.createTime);
    document.getElementById('modalFinishTime').textContent = formatDateTime(task.finishTime) || '-';
    document.getElementById('modalStatus').innerHTML = getStatusBadge(task.status);
    document.getElementById('modalResult').textContent = task.result || '-';
    
    // 显示HTTP负载内容（需要从任务详情API获取）
    loadTaskDetail(taskId);
    
    // 控制下载按钮显示
    const downloadBtn = document.getElementById('modalDownloadBtn');
    if (downloadBtn) {
        downloadBtn.style.display = task.hasReport ? 'inline-flex' : 'none';
    }
    
    // 显示模态框
    showModal('taskDetailModal');
}

/**
 * 加载任务详情
 */
async function loadTaskDetail(taskId) {
    try {
        const response = await fetch(`/api/plugins/httpLoadSimulator/tasks/${taskId}/status`);
        const result = await response.json();
        
        if (result.success && result.data) {
            // 这里需要扩展API返回HTTP负载内容
            const httpPayloadElement = document.getElementById('modalHttpPayload');
            if (httpPayloadElement) {
                // 暂时显示占位符，实际需要从任务数据中获取
                httpPayloadElement.textContent = '暂无详细HTTP负载信息';
            }
        }
    } catch (error) {
        console.error('加载任务详情失败:', error);
    }
}

/**
 * 下载任务报文压缩包
 */
function downloadTaskReport(taskId) {
    // 显示下载提示
    showAlert('正在生成报文压缩包，请稍候...', 'info');

    // 创建隐藏的下载链接
    const link = document.createElement('a');
    link.href = `/api/plugins/httpLoadSimulator/tasks/${taskId}/download-report`;
    link.download = `http_load_report_${taskId}.zip`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // 延迟显示完成提示
    setTimeout(() => {
        showAlert('报文压缩包下载已开始', 'success');
    }, 1000);
}

/**
 * 显示删除确认
 */
function showDeleteConfirm(taskId) {
    currentTaskId = taskId;
    showModal('deleteConfirmModal');
}

/**
 * 确认删除
 */
async function confirmDelete() {
    if (!currentTaskId) return;
    
    const confirmBtn = document.getElementById('confirmDeleteBtn');
    const originalText = confirmBtn.innerHTML;
    
    try {
        // 禁用按钮
        confirmBtn.disabled = true;
        confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i><span>删除中...</span>';
        
        const response = await fetch(`/api/plugins/httpLoadSimulator/tasks/${currentTaskId}/delete`, {
            method: 'DELETE'
        });
        
        const result = await response.json();
        
        if (result.success) {
            showAlert('任务删除成功', 'success');
            closeDeleteConfirm();
            // 重新加载任务列表
            await loadTasks();
        } else {
            throw new Error(result.message || '删除任务失败');
        }
        
    } catch (error) {
        console.error('删除任务失败:', error);
        showAlert(`删除任务失败: ${error.message}`, 'error');
    } finally {
        // 恢复按钮
        confirmBtn.disabled = false;
        confirmBtn.innerHTML = originalText;
    }
}

/**
 * 刷新任务列表
 */
async function refreshTasks() {
    await loadTasks();
    showAlert('任务列表已刷新', 'success');
}

/**
 * 关闭任务详情
 */
function closeTaskDetail() {
    hideModal('taskDetailModal');
    currentTaskId = null;
}

/**
 * 关闭删除确认
 */
function closeDeleteConfirm() {
    hideModal('deleteConfirmModal');
    currentTaskId = null;
}

/**
 * 显示模态框
 */
function showModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.add('show');
        document.body.style.overflow = 'hidden';
    }
}

/**
 * 隐藏模态框
 */
function hideModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.remove('show');
        document.body.style.overflow = '';
    }
}

/**
 * 关闭所有模态框
 */
function closeAllModals() {
    const modals = document.querySelectorAll('.modal.show');
    modals.forEach(modal => {
        modal.classList.remove('show');
    });
    document.body.style.overflow = '';
    currentTaskId = null;
}

/**
 * 切换全选
 */
function toggleSelectAll(checkbox) {
    const taskCheckboxes = document.querySelectorAll('.task-checkbox');
    taskCheckboxes.forEach(cb => {
        cb.checked = checkbox.checked;
    });
    updateBatchActions();
}

/**
 * 更新批量操作
 */
function updateBatchActions() {
    const selectedCheckboxes = document.querySelectorAll('.task-checkbox:checked');
    const batchActions = document.getElementById('batchActions');
    const selectedCount = document.getElementById('selectedCount');

    if (selectedCheckboxes.length > 0) {
        batchActions.style.display = 'flex';
        selectedCount.textContent = selectedCheckboxes.length;
    } else {
        batchActions.style.display = 'none';
    }

    // 更新全选复选框状态
    const selectAllCheckbox = document.getElementById('selectAllTasks');
    const allCheckboxes = document.querySelectorAll('.task-checkbox');
    if (selectAllCheckbox && allCheckboxes.length > 0) {
        selectAllCheckbox.checked = selectedCheckboxes.length === allCheckboxes.length;
        selectAllCheckbox.indeterminate = selectedCheckboxes.length > 0 && selectedCheckboxes.length < allCheckboxes.length;
    }
}

/**
 * 取消批量选择
 */
function cancelBatchSelection() {
    const checkboxes = document.querySelectorAll('.task-checkbox, #selectAllTasks');
    checkboxes.forEach(cb => {
        cb.checked = false;
        cb.indeterminate = false;
    });
    updateBatchActions();
}

/**
 * 批量删除任务
 */
async function batchDeleteTasks() {
    const selectedCheckboxes = document.querySelectorAll('.task-checkbox:checked');
    if (selectedCheckboxes.length === 0) {
        showAlert('请选择要删除的任务', 'error');
        return;
    }

    const selectedTaskIds = Array.from(selectedCheckboxes).map(cb => cb.value);

    if (!confirm(`确定要删除选中的 ${selectedTaskIds.length} 个任务吗？删除后将无法恢复。`)) {
        return;
    }

    try {
        // 显示加载状态
        const batchActions = document.getElementById('batchActions');
        const originalHTML = batchActions.innerHTML;
        batchActions.innerHTML = `
            <div class="loading-text">
                <i class="fas fa-spinner fa-spin"></i>
                正在删除任务...
            </div>
        `;

        // 调用批量删除API
        const response = await fetch('/api/plugins/httpLoadSimulator/tasks/batch-delete', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                taskIds: selectedTaskIds
            })
        });

        const result = await response.json();

        if (result.success) {
            showAlert(`成功删除 ${result.data.deletedCount} 个任务`, 'success');

            // 重新加载任务列表
            await loadTasks();

            // 取消选择状态
            cancelBatchSelection();
        } else {
            showAlert(result.message || '批量删除失败', 'error');
            // 恢复按钮状态
            batchActions.innerHTML = originalHTML;
        }

    } catch (error) {
        console.error('批量删除失败:', error);
        showAlert(`批量删除失败: ${error.message}`, 'error');

        // 恢复按钮状态
        const batchActions = document.getElementById('batchActions');
        batchActions.innerHTML = originalHTML;
    }
}

/**
 * 显示提示框
 */
function showAlert(message, type = 'info') {
    const alertContainer = document.getElementById('alertContainer');
    if (!alertContainer) return;

    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type}`;

    const iconMap = {
        'success': 'fas fa-check-circle',
        'error': 'fas fa-exclamation-circle',
        'info': 'fas fa-info-circle'
    };

    alertDiv.innerHTML = `
        <div class="alert-icon">
            <i class="${iconMap[type] || iconMap.info}"></i>
        </div>
        <div class="alert-content">
            <div class="alert-message">${message}</div>
        </div>
    `;

    alertContainer.appendChild(alertDiv);

    // 显示动画
    setTimeout(() => {
        alertDiv.classList.add('show');
    }, 100);

    // 自动隐藏
    setTimeout(() => {
        alertDiv.classList.remove('show');
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 300);
    }, 4000);
}
