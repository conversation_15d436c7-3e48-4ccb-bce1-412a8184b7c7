/**
 * 网络模拟访问插件 - 任务结果页面脚本
 */

let tasks = [];
let currentTaskId = null;

document.addEventListener('DOMContentLoaded', function() {
    console.log('任务结果页面已加载');
    
    // 初始化页面
    initializePage();
});

/**
 * 初始化页面
 */
function initializePage() {
    // 加载任务列表
    loadTasks();
    
    // 绑定事件监听器
    bindEventListeners();
}

/**
 * 绑定事件监听器
 */
function bindEventListeners() {
    // 绑定模态框关闭事件
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('modal')) {
            closeAllModals();
        }
    });
    
    // 绑定ESC键关闭模态框
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            closeAllModals();
        }
    });
}

/**
 * 加载任务列表
 */
async function loadTasks() {
    const loadingState = document.getElementById('loadingState');
    const emptyState = document.getElementById('emptyState');
    const taskList = document.getElementById('taskList');
    
    try {
        // 显示加载状态
        showElement(loadingState);
        hideElement(emptyState);
        hideElement(taskList);
        
        const response = await fetch('/api/plugins/networkSimulator/tasks');
        const result = await response.json();
        
        if (result.success) {
            tasks = result.data || [];
            
            if (tasks.length === 0) {
                // 显示空状态
                hideElement(loadingState);
                showElement(emptyState);
                hideElement(taskList);
            } else {
                // 显示任务列表
                hideElement(loadingState);
                hideElement(emptyState);
                showElement(taskList);
                renderTaskList();
            }
        } else {
            throw new Error(result.message || '加载任务列表失败');
        }
        
    } catch (error) {
        console.error('加载任务列表失败:', error);
        hideElement(loadingState);
        showAlert(`加载任务列表失败: ${error.message}`, 'error');
    }
}

/**
 * 渲染任务列表
 */
function renderTaskList() {
    const taskBody = document.getElementById('taskBody');
    if (!taskBody) return;
    
    taskBody.innerHTML = '';
    
    tasks.forEach(task => {
        const taskRow = createTaskRow(task);
        taskBody.appendChild(taskRow);
    });
}

/**
 * 创建任务行
 */
function createTaskRow(task) {
    const row = document.createElement('div');
    row.className = 'task-row';
    
    // 格式化时间
    const createTime = formatDateTime(task.createTime);
    
    // 获取状态显示
    const statusBadge = getStatusBadge(task.status);
    
    row.innerHTML = `
        <div class="task-col task-select">
            <input type="checkbox" class="task-checkbox" value="${task.id}" onchange="updateBatchActions()">
        </div>
        <div class="task-col task-id">${task.id}</div>
        <div class="task-col task-name">${escapeHtml(task.name)}</div>
        <div class="task-col">${statusBadge}</div>
        <div class="task-col task-time">${createTime}</div>
        <div class="task-col task-result">
            ${task.status === 'completed' && task.hasReport ? `
                <button class="btn btn-sm btn-success" onclick="downloadTaskReport('${task.id}')" title="下载报文压缩包">
                    <i class="fas fa-download"></i> 下载报文
                </button>
            ` : task.status === 'completed' ? `
                <span class="text-muted">无报文文件</span>
            ` : task.status === 'failed' ? `
                <span class="text-danger">执行失败</span>
            ` : `
                <span class="text-info">执行中...</span>
            `}
        </div>
        <div class="task-col task-actions">
            <button class="action-icon view" onclick="viewTaskDetail('${task.id}')" title="查看详情">
                <i class="fas fa-eye"></i>
            </button>
            <button class="action-icon delete" onclick="showDeleteConfirm('${task.id}')" title="删除任务">
                <i class="fas fa-trash"></i>
            </button>
        </div>
    `;
    
    return row;
}

/**
 * 获取状态徽章
 */
function getStatusBadge(status) {
    const statusMap = {
        'pending': { text: '等待中', class: 'status-pending' },
        'running': { text: '执行中', class: 'status-running' },
        'completed': { text: '已完成', class: 'status-completed' },
        'failed': { text: '失败', class: 'status-failed' }
    };
    
    const statusInfo = statusMap[status] || { text: status, class: 'status-pending' };
    return `<span class="status-badge ${statusInfo.class}">${statusInfo.text}</span>`;
}

/**
 * 格式化日期时间
 */
function formatDateTime(dateTimeStr) {
    if (!dateTimeStr) return '-';
    
    try {
        const date = new Date(dateTimeStr);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });
    } catch (error) {
        return dateTimeStr;
    }
}

/**
 * HTML转义
 */
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

/**
 * 查看任务详情
 */
function viewTaskDetail(taskId) {
    const task = tasks.find(t => t.id === taskId);
    if (!task) return;
    
    currentTaskId = taskId;
    
    // 填充详情信息
    document.getElementById('detailTaskId').textContent = task.id;
    document.getElementById('detailTaskName').textContent = task.name;
    document.getElementById('detailTaskStatus').innerHTML = getStatusBadge(task.status);
    document.getElementById('detailCreateTime').textContent = formatDateTime(task.createTime);
    document.getElementById('detailFinishTime').textContent = formatDateTime(task.finishTime);
    document.getElementById('detailResult').textContent = task.result || '-';
    
    // 控制下载按钮显示
    const downloadBtn = document.getElementById('modalDownloadBtn');
    if (downloadBtn) {
        downloadBtn.style.display = task.hasReport ? 'inline-flex' : 'none';
    }
    
    // 显示模态框
    showModal('taskDetailModal');
}

/**
 * 下载任务报文压缩包
 */
function downloadTaskReport(taskId) {
    // 显示下载提示
    showAlert('正在生成报文压缩包，请稍候...', 'info');

    // 创建隐藏的下载链接
    const link = document.createElement('a');
    link.href = `/api/plugins/networkSimulator/tasks/${taskId}/download-report`;
    link.download = `network_simulation_report_${taskId}.zip`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // 延迟显示完成提示
    setTimeout(() => {
        showAlert('报文压缩包下载已开始', 'success');
    }, 1000);
}

/**
 * 下载任务结果（保留原有功能）
 */
function downloadTaskResult(taskId) {
    window.open(`/api/plugins/networkSimulator/tasks/${taskId}/download`, '_blank');
}

/**
 * 显示删除确认
 */
function showDeleteConfirm(taskId) {
    currentTaskId = taskId;
    showModal('deleteConfirmModal');
}

/**
 * 确认删除
 */
async function confirmDelete() {
    if (!currentTaskId) return;
    
    const confirmBtn = document.getElementById('confirmDeleteBtn');
    const originalText = confirmBtn.innerHTML;
    
    try {
        // 禁用按钮
        confirmBtn.disabled = true;
        confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i><span>删除中...</span>';
        
        const response = await fetch(`/api/plugins/networkSimulator/tasks/${currentTaskId}/delete`, {
            method: 'DELETE'
        });
        
        const result = await response.json();
        
        if (result.success) {
            showAlert('任务删除成功', 'success');
            closeDeleteConfirm();
            // 重新加载任务列表
            await loadTasks();
        } else {
            throw new Error(result.message || '删除任务失败');
        }
        
    } catch (error) {
        console.error('删除任务失败:', error);
        showAlert(`删除任务失败: ${error.message}`, 'error');
    } finally {
        // 恢复按钮
        confirmBtn.disabled = false;
        confirmBtn.innerHTML = originalText;
    }
}

/**
 * 刷新任务列表
 */
async function refreshTasks() {
    await loadTasks();
    showAlert('任务列表已刷新', 'success');
}

/**
 * 关闭任务详情
 */
function closeTaskDetail() {
    hideModal('taskDetailModal');
    currentTaskId = null;
}

/**
 * 关闭删除确认
 */
function closeDeleteConfirm() {
    hideModal('deleteConfirmModal');
    currentTaskId = null;
}

/**
 * 显示模态框
 */
function showModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.add('show');
        document.body.style.overflow = 'hidden';
    }
}

/**
 * 隐藏模态框
 */
function hideModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.remove('show');
        document.body.style.overflow = '';
    }
}

/**
 * 关闭所有模态框
 */
function closeAllModals() {
    const modals = document.querySelectorAll('.modal.show');
    modals.forEach(modal => {
        modal.classList.remove('show');
    });
    document.body.style.overflow = '';
    currentTaskId = null;
}

/**
 * 显示元素
 */
function showElement(element) {
    if (element) {
        element.style.display = 'block';
    }
}

/**
 * 隐藏元素
 */
function hideElement(element) {
    if (element) {
        element.style.display = 'none';
    }
}

/**
 * 显示提示信息
 */
function showAlert(message, type = 'info') {
    // 创建提示框
    const alert = document.createElement('div');
    alert.className = `alert alert-${type}`;
    alert.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        border-radius: 8px;
        color: white;
        font-weight: 600;
        z-index: 1001;
        max-width: 400px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        transform: translateX(100%);
        transition: transform 0.3s ease;
    `;

    // 设置背景色
    const colors = {
        'success': '#48bb78',
        'error': '#f56565',
        'warning': '#ed8936',
        'info': '#4299e1'
    };
    alert.style.background = colors[type] || colors.info;

    alert.textContent = message;

    document.body.appendChild(alert);

    // 显示动画
    setTimeout(() => {
        alert.style.transform = 'translateX(0)';
    }, 100);

    // 自动隐藏
    setTimeout(() => {
        alert.style.transform = 'translateX(100%)';
        setTimeout(() => {
            if (alert.parentNode) {
                alert.parentNode.removeChild(alert);
            }
        }, 300);
    }, 3000);
}

// ==================== 批量删除功能 ====================

/**
 * 切换全选状态
 */
function toggleSelectAll(checkbox) {
    const taskCheckboxes = document.querySelectorAll('.task-checkbox');
    taskCheckboxes.forEach(cb => {
        cb.checked = checkbox.checked;
    });
    updateBatchActions();
}

/**
 * 更新批量操作按钮显示状态
 */
function updateBatchActions() {
    const selectedCheckboxes = document.querySelectorAll('.task-checkbox:checked');
    const batchActions = document.getElementById('batchActions');
    const selectAllCheckbox = document.getElementById('selectAllTasks');

    if (selectedCheckboxes.length > 0) {
        batchActions.style.display = 'flex';
    } else {
        batchActions.style.display = 'none';
    }

    // 更新全选复选框状态
    const allCheckboxes = document.querySelectorAll('.task-checkbox');
    if (allCheckboxes.length > 0) {
        if (selectedCheckboxes.length === allCheckboxes.length) {
            selectAllCheckbox.checked = true;
            selectAllCheckbox.indeterminate = false;
        } else if (selectedCheckboxes.length > 0) {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = true;
        } else {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = false;
        }
    }
}

/**
 * 取消批量选择
 */
function cancelBatchSelection() {
    const allCheckboxes = document.querySelectorAll('.task-checkbox, #selectAllTasks');
    allCheckboxes.forEach(cb => {
        cb.checked = false;
        cb.indeterminate = false;
    });
    updateBatchActions();
}

/**
 * 批量删除任务
 */
async function batchDeleteTasks() {
    const selectedCheckboxes = document.querySelectorAll('.task-checkbox:checked');

    if (selectedCheckboxes.length === 0) {
        showAlert('请先选择要删除的任务', 'warning');
        return;
    }

    const selectedTaskIds = Array.from(selectedCheckboxes).map(cb => cb.value);
    const taskNames = selectedTaskIds.map(id => {
        const task = tasks.find(t => t.id === id);
        return task ? task.name : id;
    });

    // 显示确认对话框
    const confirmMessage = `确定要删除以下 ${selectedTaskIds.length} 个任务吗？\n\n${taskNames.join('\n')}\n\n此操作不可撤销！`;

    if (!confirm(confirmMessage)) {
        return;
    }

    try {
        // 显示加载状态
        const batchActions = document.getElementById('batchActions');
        const originalHTML = batchActions.innerHTML;
        batchActions.innerHTML = `
            <div class="loading-text">
                <i class="fas fa-spinner fa-spin"></i>
                正在删除任务...
            </div>
        `;

        // 调用批量删除API
        const response = await fetch('/api/plugins/networkSimulator/tasks/batch-delete', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                taskIds: selectedTaskIds
            })
        });

        const result = await response.json();

        if (result.success) {
            showAlert(`成功删除 ${result.data.deletedCount} 个任务`, 'success');

            // 重新加载任务列表
            await loadTasks();

            // 取消选择状态
            cancelBatchSelection();
        } else {
            showAlert(result.message || '批量删除失败', 'error');
            // 恢复按钮状态
            batchActions.innerHTML = originalHTML;
        }

    } catch (error) {
        console.error('批量删除任务失败:', error);
        showAlert('网络错误，请稍后重试', 'error');

        // 恢复按钮状态
        const batchActions = document.getElementById('batchActions');
        batchActions.innerHTML = originalHTML;
    }
}
