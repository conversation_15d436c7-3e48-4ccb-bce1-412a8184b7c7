{% extends "base.html" %}

{% block title %}报文回放工具 - {{ app_name }}{% endblock %}

{% block content %}
<div class="pcap-replay-container">
    <!-- 页面头部 -->
    <div class="page-header">
        <div class="header-content">
            <div class="header-title">
                <i class="fas fa-play-circle me-3"></i>
            </div>
            <div class="header-actions">
                <a class="action-btn active" href="/plugins/pcapPlay/">
                    <i class="fas fa-cog"></i>
                    <span>系统设置</span>
                </a>
                <a class="action-btn" href="/plugins/pcapPlay/create-task">
                    <i class="fas fa-plus-circle"></i>
                    <span>新建任务</span>
                </a>
                <a class="action-btn" href="/plugins/pcapPlay/task-results">
                    <i class="fas fa-list-alt"></i>
                    <span>任务结果</span>
                </a>
                <a class="action-btn" href="/plugins/pcapPlay/runtime-logs">
                    <i class="fas fa-file-alt"></i>
                    <span>运行日志</span>
                </a>
            </div>
        </div>
    </div>

    <!-- 配置面板 -->
    <div class="content-panels">
        <div class="panels-grid">
            <!-- 当前配置 -->
            <div class="panel config-panel">
                <div class="panel-header">
                    <div class="panel-icon">
                        <i class="fas fa-info-circle"></i>
                    </div>
                    <div class="panel-title">
                        <h3>当前配置</h3>
                        <p>查看当前系统配置信息</p>
                    </div>
                </div>
                <div class="panel-body">
                    <div class="config-grid">
                        <div class="config-item">
                            <div class="config-label">
                                <i class="fas fa-network-wired"></i>
                                <span>设备IP地址</span>
                            </div>
                            <div class="config-value" id="currentDeviceIp">*************</div>
                        </div>
                        <div class="config-item">
                            <div class="config-label">
                                <i class="fas fa-user"></i>
                                <span>用户名</span>
                            </div>
                            <div class="config-value" id="currentUsername">jbliao</div>
                        </div>
                        <div class="config-item">
                            <div class="config-label">
                                <i class="fas fa-lock"></i>
                                <span>密码</span>
                            </div>
                            <div class="config-value" id="currentPassword">••••••</div>
                        </div>
                        <div class="config-item">
                            <div class="config-label">
                                <i class="fas fa-cogs"></i>
                                <span>网络模式</span>
                            </div>
                            <div class="config-value" id="currentNetworkMode">TAP模式</div>
                        </div>
                        <div class="config-item">
                            <div class="config-label">
                                <i class="fas fa-ethernet"></i>
                                <span>主接口</span>
                            </div>
                            <div class="config-value" id="currentMainPort">ens160</div>
                        </div>
                        <div class="config-item" id="currentBackupPortItem" style="visibility: hidden; opacity: 0;">
                            <div class="config-label">
                                <i class="fas fa-ethernet"></i>
                                <span>备接口</span>
                            </div>
                            <div class="config-value" id="currentBackupPort">ens160</div>
                        </div>
                    </div>
                </div>
                <div class="panel-footer">
                    <button type="button" class="btn-refresh" onclick="loadConfig()">
                        <i class="fas fa-sync-alt"></i>
                        <span>刷新配置</span>
                    </button>
                </div>
            </div>

            <!-- 被测设备配置 -->
            <div class="panel config-panel">
                <div class="panel-header">
                    <div class="panel-icon">
                        <i class="fas fa-server"></i>
                    </div>
                    <div class="panel-title">
                        <h3>被测设备配置</h3>
                        <p>配置目标设备连接信息</p>
                    </div>
                </div>
                <div class="panel-body">
                    <div class="form-section">
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-network-wired"></i>
                                <span>设备IP地址</span>
                            </label>
                            <input type="text" class="form-input" id="deviceIp" placeholder="*************">
                        </div>
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-user"></i>
                                    <span>用户名</span>
                                </label>
                                <input type="text" class="form-input" id="deviceUsername" placeholder="jbliao">
                            </div>
                            <div class="form-group">
                                <label class="form-label">
                                    <i class="fas fa-lock"></i>
                                    <span>密码</span>
                                </label>
                                <input type="password" class="form-input" id="devicePassword" placeholder="••••••">
                            </div>
                        </div>
                        <div class="form-checkbox">
                            <input class="checkbox-input" type="checkbox" id="saveCredentials" checked>
                            <label class="checkbox-label" for="saveCredentials">
                                <span class="checkbox-mark"></span>
                                <span class="checkbox-text">保存用户名和密码以便自动登录到设备</span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 报文回放配置 -->
            <div class="panel config-panel">
                <div class="panel-header">
                    <div class="panel-icon">
                        <i class="fas fa-play-circle"></i>
                    </div>
                    <div class="panel-title">
                        <h3>报文回放配置</h3>
                        <p>设置网络接口和回放模式</p>
                    </div>
                </div>
                <div class="panel-body">
                    <div class="form-section">
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-cogs"></i>
                                <span>网络模式</span>
                            </label>
                            <select class="form-select" id="replayMode">
                                <option value="tap">TAP模式 - 旁路监听</option>
                                <option value="bridge">串联模式</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-ethernet"></i>
                                <span>主接口</span>
                            </label>
                            <select class="form-select" id="interface1">
                                <option value="">-- 请选择网络接口 --</option>
                            </select>
                        </div>
                        <div class="form-group" id="interface2Group" style="visibility: hidden;">
                            <label class="form-label">
                                <i class="fas fa-ethernet"></i>
                                <span>备接口 (仅串联模式)</span>
                            </label>
                            <select class="form-select" id="interface2">
                                <option value="">-- 请选择网络接口 --</option>
                            </select>
                        </div>
                        <div class="info-tip">
                            <i class="fas fa-info-circle"></i>
                            <span>TAP模式适用于旁路监听，串联模式适用于内联部署</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 操作按钮 -->
    <div class="action-section">
        <button type="button" class="btn-primary-large" onclick="saveConfig()">
            <i class="fas fa-save"></i>
            <span>保存配置</span>
        </button>
    </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<!-- 引入通用样式框架 -->
<link href="{{ url_for('static', filename='plugins/pcapPlay/css/pcap-replay-common.css') }}" rel="stylesheet">

<style>
/* 报文回放工具 - 系统设置页面特定样式 */

/* 配置面板网格布局 */

.panels-grid {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 16px;
    align-items: start;
    max-width: none;
    height: auto;
}

/* 配置面板 */
.config-panel {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid #e5e7eb;
    height: fit-content;
    min-height: 500px;
    max-height: 500px;
    display: flex;
    flex-direction: column;
}

.config-panel:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 24px rgba(0, 0, 0, 0.12);
}

/* 面板头部 */
.panel-header {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px 20px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-bottom: 1px solid #e5e7eb;
    flex-shrink: 0;
}

.panel-icon {
    width: 36px;
    height: 36px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.panel-title h3 {
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 2px 0;
}

.panel-title p {
    font-size: 12px;
    color: #6b7280;
    margin: 0;
}

/* 面板内容 */
.panel-body {
    padding: 12px 20px;
    flex: 1;
    overflow-y: auto;
    min-height: 0;
}

/* 配置网格 */
.config-grid {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.config-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 0;
    border-bottom: 1px solid #f3f4f6;
}

.config-item:last-child {
    border-bottom: none;
}

.config-label {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #6b7280;
    font-size: 13px;
    font-weight: 500;
}

.config-label i {
    color: #667eea;
    width: 14px;
    font-size: 12px;
}

.config-value {
    color: #1f2937;
    font-weight: 600;
    font-size: 13px;
    background: #f9fafb;
    padding: 6px 10px;
    border-radius: 6px;
    border: 1px solid #e5e7eb;
    min-width: 100px;
    text-align: right;
}

/* 表单区域 */
.form-section {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.form-label {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #374151;
    font-size: 13px;
    font-weight: 600;
}

.form-label i {
    color: #667eea;
    width: 14px;
    font-size: 12px;
}

.form-input, .form-select {
    padding: 8px 12px;
    background: white;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    color: #1f2937;
    font-size: 13px;
    transition: all 0.3s ease;
    font-family: inherit;
}

.form-input:focus, .form-select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-input::placeholder {
    color: #9ca3af;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
}

/* 复选框样式 */
.form-checkbox {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    margin-top: 4px;
}

.checkbox-input {
    display: none;
}

.checkbox-label {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    cursor: pointer;
    color: #6b7280;
    font-size: 12px;
    line-height: 1.4;
}

.checkbox-mark {
    width: 16px;
    height: 16px;
    border: 2px solid #d1d5db;
    border-radius: 4px;
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    flex-shrink: 0;
    margin-top: 1px;
}

.checkbox-input:checked + .checkbox-label .checkbox-mark {
    background: #667eea;
    border-color: #667eea;
}

.checkbox-input:checked + .checkbox-label .checkbox-mark::after {
    content: '✓';
    color: white;
    font-size: 10px;
    font-weight: bold;
}

/* 信息提示 */
.info-tip {
    display: flex;
    align-items: center;
    gap: 6px;
    background: #eff6ff;
    border: 1px solid #bfdbfe;
    border-radius: 6px;
    padding: 6px 10px;
    color: #1e40af;
    font-size: 11px;
    margin-top: 4px;
}

.info-tip i {
    color: #3b82f6;
    flex-shrink: 0;
    font-size: 12px;
}

/* 面板底部 */
.panel-footer {
    padding: 10px 20px;
    background: #f9fafb;
    border-top: 1px solid #e5e7eb;
    flex-shrink: 0;
}

.btn-refresh {
    display: flex;
    align-items: center;
    gap: 6px;
    width: 100%;
    padding: 8px 16px;
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    border: none;
    border-radius: 8px;
    color: white;
    font-size: 13px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-refresh:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
}

.btn-refresh i {
    font-size: 14px;
}

/* 操作按钮区域 */
.action-section {
    display: flex;
    justify-content: center;
    padding: 16px 0;
    margin: 0 -24px;
    flex-shrink: 0;
}

.btn-primary-large {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 24px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 12px;
    color: white;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.btn-primary-large:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
}

.btn-primary-large i {
    font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 1400px) {
    .panels-grid {
        grid-template-columns: 1fr 1fr;
        gap: 20px;
    }
}

@media (max-width: 1024px) {
    .panels-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .pcap-replay-container {
        padding: 0 24px;
    }

    .page-header {
        margin: 0 -24px 32px -24px;
    }

    .config-panels {
        margin: 0 -24px 32px -24px;
        padding: 24px;
    }

    .action-section {
        margin: 0 -24px;
    }
}

@media (max-width: 768px) {
    .pcap-replay-container {
        padding: 0 16px;
    }

    .page-header {
        margin: 0 -16px 24px -16px;
    }

    .config-panels {
        margin: 0 -16px 24px -16px;
        padding: 16px;
    }

    .action-section {
        margin: 0 -16px;
    }

    .header-content {
        flex-direction: column;
        gap: 20px;
        padding: 24px 20px;
        text-align: center;
    }

    .header-title {
        flex-direction: column;
        gap: 12px;
    }

    .header-title i {
        font-size: 36px;
    }

    .header-title h1 {
        font-size: 24px;
    }

    .header-actions {
        flex-direction: column;
        width: 100%;
    }

    .action-btn {
        justify-content: center;
    }

    .panel-header {
        padding: 20px 20px;
    }

    .panel-body {
        padding: 20px;
    }

    .panel-footer {
        padding: 16px 20px;
    }

    .form-row {
        grid-template-columns: 1fr;
        gap: 12px;
    }

    .btn-primary-large {
        padding: 14px 24px;
        font-size: 15px;
    }
}

@media (max-width: 480px) {
    .pcap-replay-container {
        padding: 0 12px;
    }

    .page-header {
        margin: 0 -12px 20px -12px;
        border-radius: 12px;
    }

    .config-panels {
        margin: 0 -12px 20px -12px;
        padding: 12px;
        border-radius: 12px;
    }

    .action-section {
        margin: 0 -12px;
    }

    .panels-grid {
        gap: 16px;
    }

    .config-panel {
        border-radius: 12px;
    }
}

/* 过渡效果 */
#interface2Group, #currentBackupPortItem {
    transition: all 0.3s ease-in-out;
}

/* 选择框样式优化 */
.form-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 8px center;
    background-repeat: no-repeat;
    background-size: 14px 10px;
    padding-right: 32px;
    appearance: none;
}

.form-select option {
    background: white;
    color: #1f2937;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// 全局变量
let currentConfig = {};

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('pcapPlay配置页面已加载');



    // 加载当前配置
    loadConfig();

    // 加载网络接口
    loadNetworkInterfaces();

    // 添加回放模式变化监听器
    const replayModeSelect = document.getElementById('replayMode');
    if (replayModeSelect) {
        replayModeSelect.addEventListener('change', function() {
            updateInterfaceVisibility();
        });
        // 初始化接口显示
        updateInterfaceVisibility();
    }
});

// 加载配置
function loadConfig() {
    fetch('/api/plugins/pcapPlay/config')
        .then(response => response.json())
        .then(data => {
            if (data.success && data.data) {
                currentConfig = data.data;  // 修复：使用 data.data 而不是 data.config
                displayCurrentConfig(currentConfig);
                fillConfigForm(currentConfig);
            } else {
                console.error('加载配置失败: 响应数据格式错误', data);
                showAlert('加载配置失败: ' + (data.message || '响应数据格式错误'), 'warning');
            }
        })
        .catch(error => {
            console.error('加载配置失败:', error);
            showAlert('加载配置失败: ' + error.message, 'danger');
        });
}

// 显示当前配置
function displayCurrentConfig(config) {
    // 添加配置对象的安全检查
    if (!config || typeof config !== 'object') {
        console.error('displayCurrentConfig: 无效的配置对象', config);
        config = {}; // 使用空对象作为默认值
    }

    document.getElementById('currentDeviceIp').textContent = config.device?.ip || '未配置';
    document.getElementById('currentUsername').textContent = config.device?.username || '未配置';
    document.getElementById('currentPassword').textContent = config.device?.password ? '••••••' : '未配置';
    document.getElementById('currentNetworkMode').textContent =
        config.replay?.mode === 'tap' ? 'TAP模式' :
        config.replay?.mode === 'bridge' ? '串联模式' : '未配置';
    document.getElementById('currentMainPort').textContent = config.replay?.interface1 || '未配置';

    const backupPortItem = document.getElementById('currentBackupPortItem');
    const backupPortValue = document.getElementById('currentBackupPort');

    if (config.replay?.mode === 'bridge' && config.replay?.interface2) {
        backupPortValue.textContent = config.replay.interface2;
        backupPortItem.style.visibility = 'visible';
        backupPortItem.style.opacity = '1';
    } else {
        backupPortItem.style.visibility = 'hidden';
        backupPortItem.style.opacity = '0';
    }
}

// 填充配置表单
function fillConfigForm(config) {
    // 添加配置对象的安全检查
    if (!config || typeof config !== 'object') {
        console.error('fillConfigForm: 无效的配置对象', config);
        config = {}; // 使用空对象作为默认值
    }

    if (config.device) {
        document.getElementById('deviceIp').value = config.device.ip || '';
        document.getElementById('deviceUsername').value = config.device.username || '';
        document.getElementById('devicePassword').value = config.device.password || '';
        document.getElementById('saveCredentials').checked = config.device.saveCredentials !== false;
    }

    if (config.replay) {
        document.getElementById('replayMode').value = config.replay.mode || 'tap';
        document.getElementById('interface1').value = config.replay.interface1 || '';
        document.getElementById('interface2').value = config.replay.interface2 || '';
    }

    updateInterfaceVisibility();
}

// 加载网络接口
function loadNetworkInterfaces() {
    fetch('/api/plugins/pcapPlay/config/interfaces')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                populateInterfaceOptions(data.interfaces);
            } else {
                console.warn('加载网络接口失败:', data.error);
                // 使用默认接口选项
                populateInterfaceOptions(['ens160', 'ens192', 'eth0', 'eth1']);
            }
        })
        .catch(error => {
            console.error('加载网络接口失败:', error);
            // 使用默认接口选项
            populateInterfaceOptions(['ens160', 'ens192', 'eth0', 'eth1']);
        });
}

// 填充接口选项
function populateInterfaceOptions(interfaces) {
    const interface1Select = document.getElementById('interface1');
    const interface2Select = document.getElementById('interface2');

    // 清空现有选项
    interface1Select.innerHTML = '<option value="">-- 请选择网络接口 --</option>';
    interface2Select.innerHTML = '<option value="">-- 请选择网络接口 --</option>';

    // 添加接口选项
    interfaces.forEach(iface => {
        const option1 = new Option(iface, iface);
        const option2 = new Option(iface, iface);
        interface1Select.add(option1);
        interface2Select.add(option2);
    });
}

// 更新接口显示
function updateInterfaceVisibility() {
    const replayMode = document.getElementById('replayMode').value;
    const interface2Group = document.getElementById('interface2Group');

    if (replayMode === 'bridge') {
        interface2Group.style.visibility = 'visible';
        interface2Group.style.opacity = '1';
    } else {
        interface2Group.style.visibility = 'hidden';
        interface2Group.style.opacity = '0';
    }
}

// 保存配置
function saveConfig() {
    const deviceConfig = {
        ip: document.getElementById('deviceIp').value.trim(),
        username: document.getElementById('deviceUsername').value.trim(),
        password: document.getElementById('devicePassword').value,
        saveCredentials: document.getElementById('saveCredentials').checked
    };

    const replayConfig = {
        mode: document.getElementById('replayMode').value,
        interface1: document.getElementById('interface1').value,
        interface2: document.getElementById('interface2').value
    };

    // 验证必填字段
    if (!deviceConfig.ip) {
        showAlert('请输入设备IP地址', 'warning');
        return;
    }

    if (!deviceConfig.username) {
        showAlert('请输入用户名', 'warning');
        return;
    }

    if (!deviceConfig.password) {
        showAlert('请输入密码', 'warning');
        return;
    }

    if (!replayConfig.interface1) {
        showAlert('请选择主接口', 'warning');
        return;
    }

    if (replayConfig.mode === 'bridge' && !replayConfig.interface2) {
        showAlert('串联模式下请选择备接口', 'warning');
        return;
    }

    const config = {
        device: deviceConfig,
        replay: replayConfig
    };

    // 发送配置到后端
    fetch('/api/plugins/pcapPlay/config', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(config)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showAlert('配置保存成功', 'success');
            currentConfig = config;
            displayCurrentConfig(config);
        } else {
            showAlert('保存配置失败: ' + data.message, 'danger');
        }
    })
    .catch(error => {
        console.error('保存配置失败:', error);
        showAlert('保存配置失败: ' + error.message, 'danger');
    });
}

// 显示提示信息
function showAlert(message, type = 'info') {
    // 创建提示框容器
    let alertContainer = document.getElementById('alert-container');
    if (!alertContainer) {
        alertContainer = document.createElement('div');
        alertContainer.id = 'alert-container';
        alertContainer.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 99999;
            pointer-events: none;
            display: flex;
            flex-direction: column;
            gap: 12px;
            max-width: 400px;
        `;
        document.body.appendChild(alertContainer);
    }

    // 创建提示框
    const alertDiv = document.createElement('div');
    alertDiv.className = `custom-alert alert-${type}`;

    // 设置样式
    const alertStyles = {
        'success': {
            background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
            color: 'white',
            icon: '✓'
        },
        'warning': {
            background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',
            color: 'white',
            icon: '⚠'
        },
        'danger': {
            background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',
            color: 'white',
            icon: '✕'
        },
        'info': {
            background: 'linear-gradient(135deg, #3b82f6 0%, #2563eb 100%)',
            color: 'white',
            icon: 'ℹ'
        }
    };

    const style = alertStyles[type] || alertStyles['info'];

    alertDiv.style.cssText = `
        background: ${style.background};
        color: ${style.color};
        padding: 16px 20px;
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        font-size: 14px;
        font-weight: 500;
        line-height: 1.4;
        pointer-events: auto;
        cursor: pointer;
        transform: translateX(100%);
        opacity: 0;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
        min-width: 300px;
        max-width: 380px;
        word-wrap: break-word;
    `;

    alertDiv.innerHTML = `
        <div style="display: flex; align-items: flex-start; gap: 12px;">
            <span style="font-size: 16px; flex-shrink: 0; margin-top: 1px;">${style.icon}</span>
            <div style="flex: 1;">
                <div style="font-weight: 600; margin-bottom: 2px;">
                    ${type === 'success' ? '成功' : type === 'warning' ? '警告' : type === 'danger' ? '错误' : '信息'}
                </div>
                <div style="opacity: 0.95; font-size: 13px;">${message}</div>
            </div>
            <button type="button" style="
                background: none;
                border: none;
                color: inherit;
                font-size: 18px;
                cursor: pointer;
                padding: 0;
                margin: 0;
                opacity: 0.7;
                transition: opacity 0.2s ease;
                flex-shrink: 0;
                width: 20px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
            " onclick="this.parentElement.parentElement.remove()">×</button>
        </div>
        <div style="
            position: absolute;
            bottom: 0;
            left: 0;
            height: 3px;
            background: rgba(255, 255, 255, 0.3);
            width: 100%;
            transform-origin: left;
            animation: alertProgress 4s linear forwards;
        "></div>
    `;

    // 添加进度条动画
    const style_element = document.createElement('style');
    style_element.textContent = `
        @keyframes alertProgress {
            from { transform: scaleX(1); }
            to { transform: scaleX(0); }
        }
        .custom-alert:hover .alert-progress {
            animation-play-state: paused;
        }
    `;
    if (!document.querySelector('style[data-alert-styles]')) {
        style_element.setAttribute('data-alert-styles', 'true');
        document.head.appendChild(style_element);
    }

    alertContainer.appendChild(alertDiv);

    // 触发入场动画
    requestAnimationFrame(() => {
        alertDiv.style.transform = 'translateX(0)';
        alertDiv.style.opacity = '1';
    });

    // 4秒后自动消失
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.style.transform = 'translateX(100%)';
            alertDiv.style.opacity = '0';
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.parentNode.removeChild(alertDiv);
                }
            }, 300);
        }
    }, 4000);

    // 点击关闭
    alertDiv.addEventListener('click', () => {
        alertDiv.style.transform = 'translateX(100%)';
        alertDiv.style.opacity = '0';
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 300);
    });
}
</script>
{% endblock %}
