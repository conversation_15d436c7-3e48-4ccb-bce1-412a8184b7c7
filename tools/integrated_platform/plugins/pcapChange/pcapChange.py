#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
报文修改工具模块

该模块提供PCAP文件的IP地址修改功能，使用tcprewrite工具实现网络报文的重写。
主要功能包括：
1. 修改报文中的源IP和目标IP地址
2. 支持IP地址自增长功能
3. 生成tcpprep缓存文件
4. 批量处理PCAP文件

依赖工具：
- tcpprep: 用于分析网络流量并生成缓存文件
- tcprewrite: 用于重写网络报文

作者: LiaoJunBO
版本: 1.0.0
"""
import os
import subprocess
import shlex
import logging


class pcapChange(object):
    """
    报文修改和回放处理类

    该类封装了PCAP文件的IP地址修改功能，使用tcprewrite工具链
    来实现网络报文的重写操作。
    """

    def __init__(self):
        """
        初始化pcapChange实例

        功能：
        - 设置日志记录器
        - 初始化必要的配置参数
        """
        self.logger = logging.getLogger(__name__)

    def tcpprep(self, pcapPath):
        """
        执行tcpprep命令生成缓存文件

        Args:
            pcapPath (str): PCAP文件的完整路径

        Returns:
            str: 生成的缓存文件路径，失败时返回None

        功能：
        - 分析PCAP文件中的网络流量
        - 生成tcprewrite所需的缓存文件
        - 区分客户端和服务器端的流量

        注意：
        - 缓存文件与PCAP文件同名，扩展名为.cache
        - 使用-a client参数自动识别客户端流量
        """
        pcapName = pcapPath[:pcapPath.rfind('.')]
        cache = pcapName + '.cache'
        tcpprep = f'tcpprep -a client -i {shlex.quote(pcapPath)} -o {shlex.quote(cache)}'
        try:
            r = subprocess.getstatusoutput(tcpprep)
            if r[0] != 0:
                errorMsg = r[1].replace("\n", " ")
                self.logger.error(f'tcpprep failed with exit code {r[0]}, result: {errorMsg}')
                self.logger.error('tcpprep failed,can not get cache')
                return None
        except Exception as e:
            self.logger.error(f"Error executing tcpprep command: {str(e)}")
            return None
        return cache

    
    def rewrite(self, **kwargs):
        """
        修改PCAP文件中的IP地址和MAC地址

        Args:
            **kwargs: 关键字参数，包含：
                srcIp (str): 源IP地址，格式为xxx.xxx.xxx.xxx
                dstIp (str): 目标IP地址，格式为xxx.xxx.xxx.xxx
                pcapPath (str): 输入PCAP文件的完整路径

        Returns:
            str: 处理后的输出文件路径，失败时返回None

        功能：
        - 使用tcprewrite工具修改PCAP文件中的网络地址
        - 将所有报文的源IP和目标IP替换为指定地址
        - 设置统一的目标MAC地址
        - 支持报文分片配置
        - 自动清理临时文件

        处理流程：
        1. 验证输入参数的完整性和有效性
        2. 生成输出文件路径（原文件名_out.pcap）
        3. 调用tcpprep生成流量分析缓存
        4. 使用tcprewrite重写网络地址
        5. 清理临时缓存文件

        注意事项：
        - 输入文件必须是有效的PCAP格式
        - 需要系统安装tcprewrite工具包
        - 处理大文件时可能需要较长时间
        """
        try:
            # 验证必需参数
            required_params = ['srcIp', 'dstIp', 'pcapPath']
            for param in required_params:
                if param not in kwargs:
                    raise ValueError(f"Missing required parameter: {param}")

            srcIp = kwargs['srcIp']
            dstIp = kwargs['dstIp']
            pcapPath = kwargs['pcapPath']

            # 验证输入文件是否存在
            if not os.path.exists(pcapPath):
                raise FileNotFoundError(f"Input pcap file not found: {pcapPath}")

            # 设置目标MAC地址（固定值）
            dstMac = '00:1C:00:00:00:01'

            # 安全地处理文件路径，确保包含文件扩展名
            if '.' not in pcapPath:
                self.logger.error(f"Invalid pcap file path format: {pcapPath}")
                return None

            # 生成输出文件路径
            pcapName = pcapPath[:pcapPath.rfind('.')]
            outputfile = pcapName + '_out.pcap'

            # 获取报文分片配置文件路径
            pluginDir = os.path.dirname(os.path.abspath(__file__))
            fragConf = os.path.join(pluginDir, "uploads", "frag.conf")

            # 执行tcpprep命令生成缓存文件
            cache = self.tcpprep(pcapPath)

            if cache is None:
                self.logger.error('tcpprep failed, cannot get cache')
                return None

            # 构建并执行tcprewrite命令
            tcprewrite = (f'tcprewrite --fragroute={fragConf} --enet-dmac={dstMac},{dstMac} '
                         f'--endpoints={srcIp}:{dstIp} -i {shlex.quote(pcapPath)} '
                         f'-c {shlex.quote(cache)} -o {shlex.quote(outputfile)}')
            try:
                r = subprocess.getstatusoutput(tcprewrite)
                if r[0] != 0:
                    errorMsg = r[1].replace("\n", " ")
                    self.logger.error(f'tcprewrite failed with exit code {r[0]}, result: {errorMsg}')
                    # 清理可能创建的cache文件
                    if os.path.exists(cache):
                        try:
                            os.remove(cache)
                        except Exception as cleanup_e:
                            self.logger.error(f"Failed to cleanup cache file {cache}: {str(cleanup_e)}")
                    return None
            except Exception as e:
                self.logger.error(f"Error executing tcprewrite command: {str(e)}")
                return None

            return outputfile

        except Exception as e:
            self.logger.error(f"Error in pcapChange.rewrite: {str(e)}")
            return None

    def replay(self, **kwargs):
        """
        重放修改后的PCAP文件到网络接口

        Args:
            **kwargs: 关键字参数，包含：
                srcIp (str): 源IP地址
                dstIp (str): 目标IP地址
                pcapPath (str): 输入PCAP文件路径
                interface1 (str): 主网络接口名称（必需）
                interface2 (str): 备用网络接口名称（可选，默认与interface1相同）

        Returns:
            bool: 重放操作是否成功完成

        功能：
        - 先调用rewrite方法修改PCAP文件的IP地址
        - 使用tcpreplay工具将修改后的报文重放到指定网络接口
        - 支持双网卡重放模式
        - 自动清理临时文件

        处理流程：
        1. 验证输入参数，特别是网络接口参数
        2. 调用rewrite方法生成修改后的PCAP文件
        3. 生成tcpprep缓存文件
        4. 使用tcpreplay重放报文到网络接口
        5. 清理所有临时文件

        注意事项：
        - 需要root权限才能访问网络接口
        - 网络接口必须存在且处于活动状态
        - 重放操作可能影响网络流量，请谨慎使用
        """
        try:
            # 验证必需参数
            required_params = ['srcIp', 'dstIp', 'pcapPath', 'interface1']
            for param in required_params:
                if param not in kwargs:
                    raise ValueError(f"Missing required parameter: {param}")

            srcIp = kwargs['srcIp']
            dstIp = kwargs['dstIp']
            pcapPath = kwargs['pcapPath']
            interface1 = kwargs.get('interface1', '')
            interface2 = kwargs.get('interface2', interface1)

            # 验证网络接口参数
            if not interface1:
                self.logger.error("Network interface1 is required for packet replay")
                return False

            # 调用rewrite方法修改PCAP文件
            pcapNew = self.rewrite(srcIp=srcIp, dstIp=dstIp, pcapPath=pcapPath)

            # 为修改后的文件生成缓存
            cache = self.tcpprep(pcapNew)

            # 检查文件生成是否成功
            if cache is None or pcapNew is None:
                self.logger.error("Failed to rewrite pcap file, cannot proceed with replay")
                return False

            # 验证生成的文件是否存在
            if not os.path.exists(cache) or not os.path.exists(pcapNew):
                self.logger.error(f"Required files not found - cache: {cache}, pcapNew: {pcapNew}")
                return False

            # 构建并执行tcpreplay命令
            tcpreplay = (f'tcpreplay --loop=1 -c {shlex.quote(cache)} '
                        f'-i {interface1} -I {interface2} {shlex.quote(pcapNew)}')
            try:
                r = subprocess.getstatusoutput(tcpreplay)
                errorMsg = r[1].replace("\n", " ")
                if r[0] != 0:
                    self.logger.error(f'tcpreplay failed with exit code {r[0]}, result: {errorMsg}')
                    return False
                else:
                    self.logger.info(f'Packet replay successful, result: {errorMsg}')
            except Exception as e:
                self.logger.error(f"Error executing tcpreplay command: {str(e)}")
                return False

            # 清理临时文件
            cleanup_success = True
            if os.path.exists(cache):
                try:
                    os.remove(cache)
                    self.logger.info(f"Deleted temporary cache file: {cache}")
                except Exception as e:
                    self.logger.error(f"Failed to delete cache file {cache}: {str(e)}")
                    cleanup_success = False

            if os.path.exists(pcapNew):
                try:
                    os.remove(pcapNew)
                    self.logger.info(f"Deleted temporary pcap file: {pcapNew}")
                except Exception as e:
                    self.logger.error(f"Failed to delete pcap file {pcapNew}: {str(e)}")
                    cleanup_success = False

            return cleanup_success

        except Exception as e:
            self.logger.error(f"Error in pcapChange.replay: {str(e)}")
            return False
        
    def rewriteBatch(self, pcapFiles, srcIP, dstIP):
        """
        批量重写多个PCAP文件的IP地址

        Args:
            pcapFiles (List[str]): PCAP文件路径列表
            srcIP (str): 基础源IP地址
            dstIP (str): 基础目标IP地址

        Returns:
            List[str]: 处理成功的输出文件路径列表，失败时返回空列表

        功能：
        - 批量处理多个PCAP文件
        - 为每个文件使用不同的IP地址（自增长）
        - 避免多个文件使用相同IP地址造成冲突
        - 记录详细的处理进度和结果

        IP地址自增长规则：
        - 第1个文件：使用原始IP地址
        - 第2个文件：IP地址+1
        - 第3个文件：IP地址+2
        - 以此类推...

        注意事项：
        - 如果任何一个文件处理失败，整个批处理将停止
        - 返回空列表表示批处理失败
        - 建议在调用前验证所有输入文件的有效性
        """
        newPcapFiles = []
        count = 1
        for pcap in pcapFiles:
            # 使用IP地址自增长函数，为每个文件生成不同的IP地址
            srcIp = self._generate_ip_address(srcIP, count - 1)
            dstIp = self._generate_ip_address(dstIP, count - 1)
            pcapName = os.path.basename(pcap)

            self.logger.info(f"Rewrite pcap file {count}/{len(pcapFiles)}: {pcapName}")
            self.logger.info(f"Using srcIp: {srcIp}, dstIp: {dstIp}")

            # 调用单文件重写方法
            newPcap = self.rewrite(srcIp=srcIp, dstIp=dstIp, pcapPath=pcap)
            if newPcap:
                newPcapFiles.append(newPcap)
            else:
                self.logger.error(f"Failed to rewrite pcap file: {pcap}")
                return []  # 任何一个文件失败都返回空列表
            count += 1
        return newPcapFiles


    def _generate_ip_address(self, base_ip, increment):
        """
        生成IP地址,支持正确的自增长

        Args:
            base_ip (str): 基础IP地址，如 '*******'
            increment (int): 增长数值

        Returns:
            str: 生成的IP地址

        Examples:
            _generate_ip_address('*******', 0) -> '*******'
            _generate_ip_address('*******', 255) -> '*********'
            _generate_ip_address('*******', 256) -> '*******'
            _generate_ip_address('*******', 65536) -> '*******'
        """
        try:
            # 将IP地址转换为整数
            parts = base_ip.split('.')
            if len(parts) != 4:
                raise ValueError(f"Invalid IP address format: {base_ip}")

            # 验证每个部分都是有效的0-255范围
            for part in parts:
                if not part.isdigit() or not (0 <= int(part) <= 255):
                    raise ValueError(f"Invalid IP address part: {part}")

            # 将IP地址转换为32位整数
            ip_int = (int(parts[0]) << 24) + (int(parts[1]) << 16) + (int(parts[2]) << 8) + int(parts[3])

            # 加上增长值
            new_ip_int = ip_int + increment

            # 确保不超过32位整数的最大值（避免溢出）
            if new_ip_int > 0xFFFFFFFF:
                new_ip_int = 0xFFFFFFFF

            # 将整数转换回IP地址
            new_parts = [
                (new_ip_int >> 24) & 0xFF,
                (new_ip_int >> 16) & 0xFF,
                (new_ip_int >> 8) & 0xFF,
                new_ip_int & 0xFF
            ]

            return '.'.join(map(str, new_parts))

        except Exception as e:
            self.logger.error(f"Error generating IP address from {base_ip} with increment {increment}: {str(e)}")
            # 如果出错，返回基础IP地址
            return base_ip



