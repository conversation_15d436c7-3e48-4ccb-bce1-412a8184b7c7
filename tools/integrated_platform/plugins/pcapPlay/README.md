# 报文回放插件 (pcapReplay)

## 插件简介

报文回放插件是一个用于网络报文回放测试的工具插件，支持多种回放模式和参数配置。该插件提供了完整的Web界面，包括配置管理、任务创建、状态监控和日志查看等功能。

## 主要功能

### 1. 配置设置
- **网络接口配置**: 配置主接口和备用接口
- **回放参数设置**: 设置回放速度、循环次数、超时时间、包间延迟等参数
- **系统设置**: 配置最大并发任务数、日志保留天数等

### 2. 新建测试任务
- **任务创建**: 创建新的回放任务，设置任务名称和描述
- **文件上传**: 支持PCAP和CAP格式文件上传，最大100MB
- **参数配置**: 为单个任务配置特定的回放参数
- **自动启动**: 支持创建后立即启动任务

### 3. 测试任务结果
- **任务列表**: 显示所有任务的列表，支持分页和筛选
- **状态监控**: 实时显示任务状态（已创建、运行中、已完成、失败、已停止）
- **进度显示**: 显示正在运行任务的执行进度
- **任务操作**: 支持启动、停止、删除任务操作
- **详情查看**: 查看任务的详细信息和执行结果

### 4. 运行日志
- **日志查看**: 查看系统日志和任务日志
- **日志过滤**: 支持按级别、来源、关键词过滤日志
- **自动刷新**: 支持自动刷新日志显示
- **日志导出**: 支持导出日志文件

## 技术架构

### 后端架构
- **插件基类**: 继承自BasePlugin，提供标准的插件接口
- **REST API**: 提供完整的RESTful API接口
- **配置管理**: JSON格式的配置文件存储
- **任务管理**: 基于文件系统的任务数据存储
- **日志系统**: 集成Python logging模块

### 前端架构
- **响应式设计**: 基于Bootstrap的响应式Web界面
- **页签界面**: 4个功能模块的页签切换
- **AJAX交互**: 基于jQuery的异步数据交互
- **文件上传**: 支持拖拽上传和进度显示
- **实时更新**: 支持任务状态和日志的实时更新

## API接口

### 配置管理API
- `GET /api/config` - 获取插件配置
- `POST /api/config` - 更新插件配置
- `GET /api/config/interfaces` - 获取网络接口列表

### 任务管理API
- `GET /api/tasks` - 获取任务列表
- `POST /api/tasks` - 创建新任务
- `GET /api/tasks/<task_id>` - 获取任务详情
- `POST /api/tasks/<task_id>/start` - 启动任务
- `POST /api/tasks/<task_id>/stop` - 停止任务
- `DELETE /api/tasks/<task_id>/delete` - 删除任务

### 文件管理API
- `POST /api/upload` - 上传PCAP文件

### 日志查询API
- `GET /api/logs` - 获取系统日志
- `GET /api/logs/<task_id>` - 获取任务日志

## 目录结构

```
pcapPlay/
├── __init__.py              # 包初始化文件
├── plugin.py                # 插件主类
├── README.md                # 说明文档
├── config.json              # 配置文件（运行时生成）
├── templates/               # 模板目录
│   └── pcapReplay/
│       └── index.html       # 主页面模板
├── static/                  # 静态文件目录
│   ├── css/                 # CSS文件
│   └── js/                  # JavaScript文件
├── uploads/                 # 上传文件目录
├── tasks/                   # 任务数据目录
└── logs/                    # 日志文件目录
```

## 配置说明

### 默认配置
```json
{
  "networkInterfaces": {
    "interface1": "",
    "interface2": ""
  },
  "replayParams": {
    "speed": 1.0,
    "loop": 1,
    "timeout": 300,
    "packetDelay": 0
  },
  "systemSettings": {
    "maxConcurrentTasks": 5,
    "logRetentionDays": 30,
    "maxFileSize": 100
  }
}
```

### 配置项说明
- **networkInterfaces**: 网络接口配置
  - `interface1`: 主网络接口
  - `interface2`: 备用网络接口
- **replayParams**: 回放参数配置
  - `speed`: 回放速度倍数（0.1-10）
  - `loop`: 循环次数（1-1000）
  - `timeout`: 超时时间（秒）
  - `packetDelay`: 包间延迟（毫秒）
- **systemSettings**: 系统设置
  - `maxConcurrentTasks`: 最大并发任务数
  - `logRetentionDays`: 日志保留天数
  - `maxFileSize`: 最大文件大小（MB）

## 使用说明

### 1. 配置设置
1. 在"配置设置"页签中配置网络接口
2. 设置默认的回放参数
3. 点击保存按钮保存配置

### 2. 创建任务
1. 切换到"新建测试任务"页签
2. 填写任务名称和描述
3. 上传PCAP文件（拖拽或点击选择）
4. 配置任务特定参数（可选）
5. 点击"创建任务"按钮

### 3. 管理任务
1. 在"测试任务结果"页签查看任务列表
2. 使用筛选功能查看特定状态的任务
3. 点击操作按钮启动、停止或删除任务
4. 点击"查看"按钮查看任务详情

### 4. 查看日志
1. 在"运行日志"页签查看系统日志
2. 使用过滤器筛选特定级别或来源的日志
3. 开启自动刷新功能实时查看日志
4. 使用导出功能下载日志文件

## 注意事项

1. **文件格式**: 仅支持.pcap和.cap格式的文件
2. **文件大小**: 单个文件不超过100MB
3. **网络权限**: 确保应用有足够的网络接口访问权限
4. **并发限制**: 同时运行的任务数量受配置限制
5. **存储空间**: 注意上传文件和日志文件的存储空间使用

## 开发说明

### 扩展功能
如需扩展插件功能，可以：
1. 在plugin.py中添加新的API接口
2. 在前端页面中添加新的功能模块
3. 修改配置文件结构添加新的配置项
4. 集成实际的报文回放工具

### 调试模式
开发时可以通过以下方式调试：
1. 查看插件日志：`self.logger.info("调试信息")`
2. 检查配置文件：查看config.json文件内容
3. 监控API调用：使用浏览器开发者工具查看网络请求
4. 查看任务数据：检查tasks目录下的JSON文件

## 版本信息

- **版本**: 1.0.0
- **作者**: 插件开发者
- **更新时间**: 2024年
- **兼容性**: 支持Python 3.6+，Flask 1.0+

## 许可证

本插件遵循MIT许可证。
