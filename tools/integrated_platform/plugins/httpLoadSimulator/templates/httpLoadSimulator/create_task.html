{% extends "base.html" %}

{% block title %}{{ pluginInfo.displayName }} - 创建任务{% endblock %}

{% block extra_css %}
<link href="{{ url_for('static', filename='plugins/httpLoadSimulator/css/style.css') }}" rel="stylesheet">
{% endblock %}

{% block content %}
<div class="http-load-simulator-container">
    <!-- 页面头部 -->
    <div class="page-header">
        <div class="header-content">
            <div class="header-title">
                <i class="fas fa-server me-3"></i>
                <h1>HTTP负载模拟访问</h1>
            </div>
            <div class="header-actions">
                <a class="action-btn active" href="/plugins/httpLoadSimulator/">
                    <i class="fas fa-plus-circle"></i>
                    <span>新建任务</span>
                </a>
                <a class="action-btn" href="/plugins/httpLoadSimulator/task-results">
                    <i class="fas fa-list-alt"></i>
                    <span>任务结果</span>
                </a>
            </div>
        </div>
    </div>

    <!-- 任务创建面板 -->
    <div class="content-panels">
        <div class="panel task-panel">
            <div class="panel-header">
                <div class="panel-icon">
                    <i class="fas fa-plus-circle"></i>
                </div>
                <div class="panel-title">
                    <h3>新建HTTP负载模拟任务</h3>
                    <p>配置HTTP负载参数后启动模拟任务，支持自定义HTTP请求头和请求体，生成完整的网络报文抓包文件</p>
                </div>
            </div>
            <div class="panel-body">
                <form id="createTaskForm" onsubmit="handleFormSubmit(event)">
                    <!-- 任务名称 -->
                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-tag"></i>
                            <span>任务名称</span>
                        </label>
                        <input type="text"
                               class="form-input"
                               id="taskName"
                               placeholder="请输入任务名称"
                               required>
                        <div class="form-hint">请输入一个有意义的任务名称，便于后续识别和管理</div>
                    </div>

                    <!-- 目标配置 -->
                    <div class="form-row">
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-bullseye"></i>
                                <span>目标IP地址</span>
                            </label>
                            <input type="text"
                                   class="form-input"
                                   id="targetIp"
                                   placeholder="127.0.0.1"
                                   value="127.0.0.1"
                                   required>
                            <div class="form-hint">目标服务器的IP地址,暂不需要改动</div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-plug"></i>
                                <span>目标端口</span>
                            </label>
                            <input type="number"
                                   class="form-input"
                                   id="targetPort"
                                   value="80"
                                   min="1"
                                   max="65535"
                                   required>
                            <div class="form-hint">目标服务端口号（1-65535）</div>
                        </div>
                    </div>

                    <!-- HTTP负载输入方式选择 -->
                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-cogs"></i>
                            <span>HTTP负载输入方式</span>
                        </label>
                        <div class="task-mode-selector">
                            <label class="radio-option">
                                <input type="radio" name="inputMode" value="manual" checked onchange="toggleInputMode()">
                                <span class="radio-label">
                                    <i class="fas fa-edit"></i>
                                    手动输入HTTP负载
                                </span>
                            </label>
                            <label class="radio-option">
                                <input type="radio" name="inputMode" value="csv" onchange="toggleInputMode()">
                                <span class="radio-label">
                                    <i class="fas fa-file-csv"></i>
                                    CSV文件批量上传
                                </span>
                            </label>
                        </div>
                        <div class="form-hint">选择HTTP负载的输入方式</div>
                    </div>

                    <!-- 手动输入HTTP负载区域 -->
                    <div id="manualInputConfig">
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-code"></i>
                                <span>HTTP负载</span>
                            </label>
                            <textarea class="form-textarea"
                                      id="httpPayload"
                                      rows="15"
                                      placeholder="请输入完整的HTTP请求，例如：&#10;POST /api/test HTTP/1.1&#10;Host: example.com&#10;Content-Type: application/json&#10;Content-Length: 25&#10;&#10;{&quot;key&quot;: &quot;value&quot;}"
                                      required></textarea>
                            <div class="form-hint">请输入完整的HTTP请求，包括请求行、请求头和请求体</div>
                        </div>
                    </div>

                    <!-- CSV文件上传区域 -->
                    <div id="csvInputConfig" style="display: none;">
                        <div class="form-group">
                            <label class="form-label">
                                <i class="fas fa-file-csv"></i>
                                <span>CSV文件上传</span>
                            </label>
                            <div class="file-upload-area" id="csvUploadArea">
                                <input type="file" id="csvFile" accept=".csv" onchange="handleCsvFileSelect(event)">
                                <div class="upload-content">
                                    <i class="fas fa-cloud-upload-alt"></i>
                                    <p>点击选择CSV文件或拖拽文件到此处</p>
                                    <small>支持.csv格式文件，最大10MB</small>
                                </div>
                            </div>
                            <div class="form-hint">
                                CSV文件格式：第一列为测试项目名称，第二列为HTTP负载内容。
                                <a href="#" onclick="downloadCsvTemplate()">下载模板文件</a>
                            </div>
                            <div id="csvFileInfo" style="display: none; margin-top: 10px;">
                                <div class="alert alert-success">
                                    <i class="fas fa-check-circle"></i>
                                    <span>文件已选择</span>
                                </div>
                                <div class="file-details">
                                    <span class="file-name" id="csvFileName">文件名</span>
                                    <span class="file-size" id="csvFileSize">文件大小</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 提交按钮 -->
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary" id="submitBtn">
                            <i class="fas fa-play"></i>
                            <span>开始模拟</span>
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="resetForm()">
                            <i class="fas fa-undo"></i>
                            <span>重置表单</span>
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- 任务状态面板 -->
        <div class="panel status-panel" id="statusPanel" style="display: none;">
            <div class="panel-header">
                <div class="panel-icon">
                    <i class="fas fa-tasks"></i>
                </div>
                <div class="panel-title">
                    <h3>任务执行状态</h3>
                    <p>实时显示任务执行进度和结果</p>
                </div>
            </div>
            <div class="panel-body">
                <div class="status-content">
                    <div class="status-item">
                        <div class="status-label">任务ID：</div>
                        <div class="status-value" id="taskId">-</div>
                    </div>
                    <div class="status-item">
                        <div class="status-label">任务状态：</div>
                        <div class="status-value" id="taskStatus">
                            <span class="status-badge status-pending">等待中</span>
                        </div>
                    </div>
                    <div class="status-item">
                        <div class="status-label">执行结果：</div>
                        <div class="status-value" id="taskResult">-</div>
                    </div>
                </div>
                
                <div class="progress-section">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="progress-text" id="progressText">准备中...</div>
                </div>

                <div class="action-buttons" id="actionButtons" style="display: none;">
                    <button class="btn btn-success" id="downloadBtn" onclick="downloadResult()">
                        <i class="fas fa-download"></i>
                        <span>下载抓包文件</span>
                    </button>
                    <button class="btn btn-info" onclick="viewResults()">
                        <i class="fas fa-eye"></i>
                        <span>查看结果</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 提示框 -->
<div class="alert-container" id="alertContainer"></div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='plugins/httpLoadSimulator/js/create_task.js') }}"></script>
{% endblock %}
