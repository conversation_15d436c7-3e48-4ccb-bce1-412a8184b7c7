# 集成工具平台 v2.0

基于插件架构的集成工具平台，支持灵活的功能扩展和模块化开发。

## 🚀 核心特性

- **插件式架构**：支持动态加载/卸载插件，模块化开发
- **统一API规范**：标准化的前后端交互接口
- **独立前端管理**：插件可以自管理前端页面和静态资源
- **插件级日志**：每个插件都有独立的日志记录器
- **配置管理**：支持插件级别的配置验证和管理
- **本地化资源**：避免外部依赖，提高加载速度

## 📦 安装和运行

1. **安装依赖**：
```bash
pip install -r requirements.txt
```

2. **运行应用**：
```bash
python run.py
```

3. **访问应用**：
   - 首页: http://127.0.0.1:8082
   - 系统API: http://127.0.0.1:8082/api/system/plugins
   - 健康检查: http://127.0.0.1:8082/health

## 🏗️ 项目架构

```
integrated_platform/
├── core/                          # 核心框架
│   ├── app.py                     # 主应用
│   ├── plugin_manager.py          # 插件管理器
│   ├── route_registry.py          # 路由注册器
│   ├── api_manager.py             # API管理器
│   ├── log_manager.py             # 日志管理器
│   └── base_plugin.py             # 插件基类
├── plugins/                       # 插件目录
│   ├── plugin_template/           # 插件开发模板
│   └── example_plugin/            # 示例插件
├── config/                        # 配置文件
│   ├── base.py                    # 基础配置
│   └── development.py             # 开发配置
├── templates/                     # 核心模板
│   ├── base.html                  # 基础模板
│   ├── index.html                 # 首页
│   └── error.html                 # 错误页面
├── static/                        # 静态资源
│   ├── css/vendor/                # 第三方CSS
│   └── js/vendor/                 # 第三方JS
├── logs/                          # 日志目录
└── run.py                         # 启动脚本
```

## 🔌 插件开发

### 快速开始

1. **复制插件模板**：
```bash
cp -r plugins/plugin_template plugins/my_plugin
cd plugins/my_plugin
```

2. **修改插件信息**：
编辑 `plugin.py` 文件中的基本信息：
```python
def get_name(self) -> str:
    return "my_plugin"

def get_display_name(self) -> str:
    return "我的插件"

def get_description(self) -> str:
    return "这是我的第一个插件"
```

3. **实现功能逻辑**：
- 添加页面路由处理函数
- 添加API接口处理函数
- 创建前端模板文件

4. **重启应用**：
```bash
python run.py
```

## 📚 API接口

### 系统API

- `GET /api/system/info` - 获取系统信息
- `GET /api/system/plugins` - 获取插件列表
- `GET /api/system/plugins/status` - 获取插件状态
- `GET /health` - 健康检查

### 插件API

- `GET /api/plugins/{plugin_name}/*` - 插件专用API
- 所有插件API都有统一的错误处理和响应格式

## 🔧 配置管理

### 环境配置

- **开发环境**: `config/development.py`
- **生产环境**: `config/production.py`
- **基础配置**: `config/base.py`

## 📝 日志管理

- **应用日志**: `logs/app.log`
- **错误日志**: `logs/error.log`
- **插件日志**: `logs/plugin_{plugin_name}.log`

## 🚀 部署

### 开发环境
```bash
export FLASK_ENV=development
python run.py
```

### 生产环境
```bash
export FLASK_ENV=production
export PORT=8080
python run.py
```

## 🎯 示例插件

平台包含两个示例插件：

1. **plugin_template** - 完整的插件开发模板
2. **example_plugin** - 简单的文本处理示例

访问 http://127.0.0.1:8082/plugins/example_plugin/ 体验示例插件功能。

## 📖 更多文档

- [插件开发指南](plugins/plugin_template/README.md)

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📄 许可证

MIT License