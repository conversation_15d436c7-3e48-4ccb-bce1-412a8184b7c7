/* jQuery 3.6.0 简化版本 - 基础功能 */

(function(global, factory) {
    "use strict";
    if (typeof module === "object" && typeof module.exports === "object") {
        module.exports = global.document ?
            factory(global, true) :
            function(w) {
                if (!w.document) {
                    throw new Error("jQuery requires a window with a document");
                }
                return factory(w);
            };
    } else {
        factory(global);
    }
})(typeof window !== "undefined" ? window : this, function(window, noGlobal) {
    "use strict";

    var arr = [];
    var document = window.document;
    var getProto = Object.getPrototypeOf;
    var slice = arr.slice;
    var concat = arr.concat;
    var push = arr.push;
    var indexOf = arr.indexOf;
    var class2type = {};
    var toString = class2type.toString;
    var hasOwn = class2type.hasOwnProperty;
    var fnToString = hasOwn.toString;
    var ObjectFunctionString = fnToString.call(Object);
    var support = {};

    var version = "3.6.0-simplified";

    function jQuery(selector, context) {
        return new jQuery.fn.init(selector, context);
    }

    jQuery.fn = jQuery.prototype = {
        jquery: version,
        constructor: jQuery,
        length: 0,

        toArray: function() {
            return slice.call(this);
        },

        get: function(num) {
            if (num == null) {
                return slice.call(this);
            }
            return num < 0 ? this[num + this.length] : this[num];
        },

        pushStack: function(elems) {
            var ret = jQuery.merge(this.constructor(), elems);
            ret.prevObject = this;
            return ret;
        },

        each: function(callback) {
            return jQuery.each(this, callback);
        },

        map: function(callback) {
            return this.pushStack(jQuery.map(this, function(elem, i) {
                return callback.call(elem, i, elem);
            }));
        },

        slice: function() {
            return this.pushStack(slice.apply(this, arguments));
        },

        first: function() {
            return this.eq(0);
        },

        last: function() {
            return this.eq(-1);
        },

        eq: function(i) {
            var len = this.length,
                j = +i + (i < 0 ? len : 0);
            return this.pushStack(j >= 0 && j < len ? [this[j]] : []);
        },

        end: function() {
            return this.prevObject || this.constructor();
        }
    };

    jQuery.extend = jQuery.fn.extend = function() {
        var options, name, src, copy, copyIsArray, clone,
            target = arguments[0] || {},
            i = 1,
            length = arguments.length,
            deep = false;

        if (typeof target === "boolean") {
            deep = target;
            target = arguments[i] || {};
            i++;
        }

        if (typeof target !== "object" && typeof target !== "function") {
            target = {};
        }

        if (i === length) {
            target = this;
            i--;
        }

        for (; i < length; i++) {
            if ((options = arguments[i]) != null) {
                for (name in options) {
                    copy = options[name];
                    if (name === "__proto__" || target === copy) {
                        continue;
                    }
                    if (deep && copy && (jQuery.isPlainObject(copy) || (copyIsArray = Array.isArray(copy)))) {
                        src = target[name];
                        if (copyIsArray && !Array.isArray(src)) {
                            clone = [];
                        } else if (!copyIsArray && !jQuery.isPlainObject(src)) {
                            clone = {};
                        } else {
                            clone = src;
                        }
                        copyIsArray = false;
                        target[name] = jQuery.extend(deep, clone, copy);
                    } else if (copy !== undefined) {
                        target[name] = copy;
                    }
                }
            }
        }
        return target;
    };

    jQuery.extend({
        expando: "jQuery" + (version + Math.random()).replace(/\D/g, ""),
        isReady: true,
        error: function(msg) {
            throw new Error(msg);
        },
        noop: function() {},
        isPlainObject: function(obj) {
            var proto, Ctor;
            if (!obj || toString.call(obj) !== "[object Object]") {
                return false;
            }
            proto = getProto(obj);
            if (!proto) {
                return true;
            }
            Ctor = hasOwn.call(proto, "constructor") && proto.constructor;
            return typeof Ctor === "function" && fnToString.call(Ctor) === ObjectFunctionString;
        },
        isEmptyObject: function(obj) {
            var name;
            for (name in obj) {
                return false;
            }
            return true;
        },
        globalEval: function(code, options) {
            var script = document.createElement("script");
            script.text = code;
            if (options && options.nonce) {
                script.nonce = options.nonce;
            }
            document.head.appendChild(script).parentNode.removeChild(script);
        },
        each: function(obj, callback) {
            var length, i = 0;
            if (isArrayLike(obj)) {
                length = obj.length;
                for (; i < length; i++) {
                    if (callback.call(obj[i], i, obj[i]) === false) {
                        break;
                    }
                }
            } else {
                for (i in obj) {
                    if (callback.call(obj[i], i, obj[i]) === false) {
                        break;
                    }
                }
            }
            return obj;
        },
        map: function(elems, callback, arg) {
            var length, value,
                i = 0,
                ret = [];
            if (isArrayLike(elems)) {
                length = elems.length;
                for (; i < length; i++) {
                    value = callback(elems[i], i, arg);
                    if (value != null) {
                        ret.push(value);
                    }
                }
            } else {
                for (i in elems) {
                    value = callback(elems[i], i, arg);
                    if (value != null) {
                        ret.push(value);
                    }
                }
            }
            return concat.apply([], ret);
        },
        merge: function(first, second) {
            var len = +second.length,
                j = 0,
                i = first.length;
            for (; j < len; j++) {
                first[i++] = second[j];
            }
            first.length = i;
            return first;
        }
    });

    function isArrayLike(obj) {
        var length = !!obj && "length" in obj && obj.length,
            type = typeof obj;
        if (type === "function" || obj === window) {
            return false;
        }
        return type === "array" || length === 0 ||
            typeof length === "number" && length > 0 && (length - 1) in obj;
    }

    var init = jQuery.fn.init = function(selector, context, root) {
        var match, elem;
        if (!selector) {
            return this;
        }
        root = root || document;
        if (typeof selector === "string") {
            if (selector[0] === "<" && selector[selector.length - 1] === ">" && selector.length >= 3) {
                match = [null, selector, null];
            } else {
                match = [null, null, selector];
            }
            if (match[1]) {
                context = context instanceof jQuery ? context[0] : context;
                jQuery.merge(this, jQuery.parseHTML(match[1], context && context.nodeType ? context.ownerDocument || context : document, true));
            } else {
                elem = document.querySelectorAll(selector);
                return jQuery.merge(this, elem);
            }
        } else if (selector.nodeType) {
            this[0] = selector;
            this.length = 1;
            return this;
        } else if (typeof selector === "function") {
            return root.ready !== undefined ? root.ready(selector) : selector(jQuery);
        }
        return jQuery.makeArray(selector, this);
    };

    init.prototype = jQuery.fn;

    jQuery.parseHTML = function(data, context, keepScripts) {
        if (typeof data !== "string") {
            return [];
        }
        if (typeof context === "boolean") {
            keepScripts = context;
            context = false;
        }
        var base, parsed, scripts;
        if (!context) {
            context = document;
        }
        parsed = /<(\w+)\s*\/?>(?:<\/\1>|)$/.exec(data);
        if (parsed) {
            return [context.createElement(parsed[1])];
        }
        base = context.createElement("div");
        base.innerHTML = data;
        return jQuery.merge([], base.childNodes);
    };

    jQuery.makeArray = function(arr, results) {
        var ret = results || [];
        if (arr != null) {
            if (isArrayLike(Object(arr))) {
                jQuery.merge(ret, typeof arr === "string" ? [arr] : arr);
            } else {
                push.call(ret, arr);
            }
        }
        return ret;
    };

    // 简化的事件处理
    jQuery.fn.extend({
        on: function(types, selector, data, fn) {
            return this.each(function() {
                if (typeof types === "string") {
                    var handler = fn || data || selector;
                    if (typeof handler === "function") {
                        this.addEventListener(types, handler);
                    }
                }
            });
        },
        off: function(types, selector, fn) {
            return this.each(function() {
                if (typeof types === "string") {
                    var handler = fn || selector;
                    if (typeof handler === "function") {
                        this.removeEventListener(types, handler);
                    }
                }
            });
        },
        click: function(handler) {
            return arguments.length > 0 ? this.on("click", handler) : this.trigger("click");
        },
        ready: function(fn) {
            if (document.readyState === "complete" || (document.readyState !== "loading" && !document.documentElement.doScroll)) {
                window.setTimeout(fn);
            } else {
                document.addEventListener("DOMContentLoaded", fn);
            }
            return this;
        }
    });

    // DOM操作
    jQuery.fn.extend({
        html: function(value) {
            if (value === undefined) {
                return this[0] ? this[0].innerHTML : null;
            }
            return this.each(function() {
                this.innerHTML = value;
            });
        },
        text: function(value) {
            if (value === undefined) {
                return this[0] ? this[0].textContent : null;
            }
            return this.each(function() {
                this.textContent = value;
            });
        },
        val: function(value) {
            if (value === undefined) {
                return this[0] ? this[0].value : null;
            }
            return this.each(function() {
                this.value = value;
            });
        },
        attr: function(name, value) {
            if (value === undefined) {
                return this[0] ? this[0].getAttribute(name) : null;
            }
            return this.each(function() {
                this.setAttribute(name, value);
            });
        },
        addClass: function(className) {
            return this.each(function() {
                if (this.classList) {
                    this.classList.add(className);
                }
            });
        },
        removeClass: function(className) {
            return this.each(function() {
                if (this.classList) {
                    this.classList.remove(className);
                }
            });
        },
        toggleClass: function(className) {
            return this.each(function() {
                if (this.classList) {
                    this.classList.toggle(className);
                }
            });
        },
        hasClass: function(className) {
            return this[0] ? this[0].classList.contains(className) : false;
        }
    });

    // AJAX简化版本
    jQuery.extend({
        ajax: function(options) {
            var xhr = new XMLHttpRequest();
            var settings = jQuery.extend({
                type: "GET",
                url: "",
                data: null,
                success: function() {},
                error: function() {},
                complete: function() {}
            }, options);

            xhr.open(settings.type, settings.url, true);
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status >= 200 && xhr.status < 300) {
                        settings.success(xhr.responseText, "success", xhr);
                    } else {
                        settings.error(xhr, "error", xhr.statusText);
                    }
                    settings.complete(xhr, xhr.status >= 200 && xhr.status < 300 ? "success" : "error");
                }
            };
            xhr.send(settings.data);
            return xhr;
        }
    });

    // 全局暴露
    var $ = jQuery;

    if (typeof noGlobal === "undefined") {
        window.jQuery = window.$ = jQuery;
    }

    return jQuery;
});

// Bootstrap JavaScript 核心功能
document.addEventListener('DOMContentLoaded', function() {
    // 下拉菜单功能
    document.querySelectorAll('[data-bs-toggle="dropdown"]').forEach(function(element) {
        element.addEventListener('click', function(e) {
            e.preventDefault();
            var menu = this.nextElementSibling;
            if (menu && menu.classList.contains('dropdown-menu')) {
                menu.classList.toggle('show');
            }
        });
    });

    // 警告框关闭功能
    document.querySelectorAll('[data-bs-dismiss="alert"]').forEach(function(element) {
        element.addEventListener('click', function() {
            var alert = this.closest('.alert');
            if (alert) {
                alert.remove();
            }
        });
    });

    // 导航栏切换功能
    document.querySelectorAll('[data-bs-toggle="collapse"]').forEach(function(element) {
        element.addEventListener('click', function() {
            var target = document.querySelector(this.getAttribute('data-bs-target'));
            if (target) {
                target.classList.toggle('show');
            }
        });
    });

    // 点击外部关闭下拉菜单
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.dropdown')) {
            document.querySelectorAll('.dropdown-menu.show').forEach(function(menu) {
                menu.classList.remove('show');
            });
        }
    });
});
