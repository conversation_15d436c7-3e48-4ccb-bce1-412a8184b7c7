#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的插件管理器
只负责插件的发现、加载和基本信息管理
"""

import os
import sys
import importlib
from typing import Dict, List, Any, Optional
from flask import Flask

from .log_manager import LogManager


class PluginManager:
    """简化的插件管理器"""

    def __init__(self, app: Optional[Flask] = None):
        self.app = app
        self.plugins: Dict[str, Any] = {}
        self.plugins_dir = None
        self.logger = LogManager.get_logger('plugin_manager')

        if app:
            self.init_app(app)

    def init_app(self, app: Flask):
        """初始化插件管理器"""
        self.app = app
        # 插件目录应该在项目根目录下，而不是core目录下
        project_root = os.path.dirname(app.root_path)
        self.plugins_dir = os.path.join(project_root, 'plugins')

        # 确保插件目录存在
        if not os.path.exists(self.plugins_dir):
            os.makedirs(self.plugins_dir)
            self.logger.info(f"创建插件目录: {self.plugins_dir}")
        else:
            self.logger.info(f"使用插件目录: {self.plugins_dir}")

    def discover_plugins(self) -> List[str]:
        """发现可用的插件"""
        if not self.plugins_dir or not os.path.exists(self.plugins_dir):
            return []

        plugins = []
        for item in os.listdir(self.plugins_dir):
            plugin_path = os.path.join(self.plugins_dir, item)

            # 检查是否为有效的插件目录
            if (os.path.isdir(plugin_path) and
                not item.startswith('_') and
                os.path.exists(os.path.join(plugin_path, '__init__.py')) and
                os.path.exists(os.path.join(plugin_path, 'plugin.py'))):
                plugins.append(item)

        self.logger.info(f"发现 {len(plugins)} 个插件: {plugins}")
        return plugins

    def loadPlugin(self, pluginName: str) -> bool:
        """加载单个插件"""
        try:
            # 添加插件路径到系统路径
            if self.plugins_dir not in sys.path:
                sys.path.insert(0, self.plugins_dir)

            # 导入插件模块
            pluginModule = importlib.import_module(f"{pluginName}.plugin")

            # 获取插件类
            if hasattr(pluginModule, 'Plugin'):
                pluginClass = getattr(pluginModule, 'Plugin')

                # 实例化插件
                pluginInstance = pluginClass(app=self.app)

                # 初始化插件
                if hasattr(pluginInstance, 'initPlugin'):
                    pluginInstance.initPlugin()

                # 存储插件实例
                self.plugins[pluginName] = pluginInstance

                self.logger.info(f"插件加载成功: {pluginName}")
                return True
            else:
                self.logger.error(f"插件 {pluginName} 中未找到 Plugin 类")
                return False

        except Exception as e:
            self.logger.error(f"加载插件 {pluginName} 失败: {str(e)}")
            return False

    def loadAllPlugins(self) -> Dict[str, bool]:
        """加载所有发现的插件"""
        plugins = self.discover_plugins()
        results = {}

        for pluginName in plugins:
            results[pluginName] = self.loadPlugin(pluginName)

        loadedCount = sum(1 for success in results.values() if success)
        self.logger.info(f"插件加载完成，成功加载 {loadedCount}/{len(plugins)} 个插件")

        return results

    def getPlugin(self, pluginName: str) -> Optional[Any]:
        """获取插件实例"""
        return self.plugins.get(pluginName)

    def getAllPlugins(self) -> Dict[str, Any]:
        """获取所有已加载的插件"""
        return self.plugins.copy()

    def getPluginInfo(self, pluginName: str) -> Optional[Dict[str, Any]]:
        """获取插件基本信息"""
        plugin = self.getPlugin(pluginName)
        if not plugin:
            return None

        # 获取插件基本信息
        info = {
            'name': getattr(plugin, 'name', pluginName),
            'displayName': getattr(plugin, 'displayName', pluginName),
            'description': getattr(plugin, 'description', ''),
            'version': getattr(plugin, 'version', '1.0.0'),
            'author': getattr(plugin, 'author', 'Unknown'),
            'navItems': getattr(plugin, 'getNavItems', lambda: [])() if hasattr(plugin, 'getNavItems') else [],
        }

        return info

    def getAllPluginInfo(self) -> List[Dict[str, Any]]:
        """获取所有插件的基本信息"""
        pluginInfoList = []

        for pluginName in self.plugins:
            info = self.getPluginInfo(pluginName)
            if info:
                pluginInfoList.append(info)

        return pluginInfoList

    def getPluginCount(self) -> int:
        """获取已加载插件数量"""
        return len(self.plugins)
