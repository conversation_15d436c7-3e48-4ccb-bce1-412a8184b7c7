/**
 * 集成工具平台主要JavaScript功能
 */

// 全局变量
window.IntegratedPlatform = {
    apiBase: '/api',
    currentTool: null,
    currentConfig: null
};

// 工具类
class ToolManager {
    constructor() {
        this.tools = [];
        this.configs = {};
    }

    // 获取所有工具
    async getTools() {
        try {
            const response = await fetch(`${window.IntegratedPlatform.apiBase}/tools`);
            const data = await response.json();
            if (data.success) {
                this.tools = data.data;
                return this.tools;
            } else {
                throw new Error(data.message);
            }
        } catch (error) {
            console.error('获取工具列表失败:', error);
            throw error;
        }
    }

    // 创建工具
    async createTool(toolData) {
        try {
            const response = await fetch(`${window.IntegratedPlatform.apiBase}/tools`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(toolData)
            });
            const data = await response.json();
            if (data.success) {
                this.tools.push(data.data);
                return data.data;
            } else {
                throw new Error(data.message);
            }
        } catch (error) {
            console.error('创建工具失败:', error);
            throw error;
        }
    }

    // 更新工具
    async updateTool(toolId, updateData) {
        try {
            const response = await fetch(`${window.IntegratedPlatform.apiBase}/tools/${toolId}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(updateData)
            });
            const data = await response.json();
            if (data.success) {
                const index = this.tools.findIndex(t => t.id === toolId);
                if (index !== -1) {
                    this.tools[index] = data.data;
                }
                return data.data;
            } else {
                throw new Error(data.message);
            }
        } catch (error) {
            console.error('更新工具失败:', error);
            throw error;
        }
    }

    // 删除工具
    async deleteTool(toolId) {
        try {
            const response = await fetch(`${window.IntegratedPlatform.apiBase}/tools/${toolId}`, {
                method: 'DELETE'
            });
            const data = await response.json();
            if (data.success) {
                this.tools = this.tools.filter(t => t.id !== toolId);
                return true;
            } else {
                throw new Error(data.message);
            }
        } catch (error) {
            console.error('删除工具失败:', error);
            throw error;
        }
    }

    // 获取工具配置
    async getToolConfigs(toolId) {
        try {
            const response = await fetch(`${window.IntegratedPlatform.apiBase}/tools/${toolId}/configs`);
            const data = await response.json();
            if (data.success) {
                this.configs[toolId] = data.data;
                return data.data;
            } else {
                throw new Error(data.message);
            }
        } catch (error) {
            console.error('获取工具配置失败:', error);
            throw error;
        }
    }

    // 创建工具配置
    async createToolConfig(toolId, configData) {
        try {
            const response = await fetch(`${window.IntegratedPlatform.apiBase}/tools/${toolId}/configs`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(configData)
            });
            const data = await response.json();
            if (data.success) {
                if (!this.configs[toolId]) {
                    this.configs[toolId] = [];
                }
                this.configs[toolId].push(data.data);
                return data.data;
            } else {
                throw new Error(data.message);
            }
        } catch (error) {
            console.error('创建工具配置失败:', error);
            throw error;
        }
    }

    // 执行工具
    async executeTool(toolId, configData) {
        try {
            const response = await fetch(`${window.IntegratedPlatform.apiBase}/tools/${toolId}/execute`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ config_data: configData })
            });
            const data = await response.json();
            if (data.success) {
                return data.data;
            } else {
                throw new Error(data.message);
            }
        } catch (error) {
            console.error('执行工具失败:', error);
            throw error;
        }
    }
}

// 日志管理器
class LogManager {
    constructor() {
        this.logs = [];
    }

    // 获取日志
    async getLogs(page = 1, perPage = 50) {
        try {
            const response = await fetch(`${window.IntegratedPlatform.apiBase}/logs?page=${page}&per_page=${perPage}`);
            const data = await response.json();
            if (data.success) {
                this.logs = data.data.logs;
                return data.data;
            } else {
                throw new Error(data.message);
            }
        } catch (error) {
            console.error('获取日志失败:', error);
            throw error;
        }
    }
}

// 通用工具函数
class Utils {
    // 显示通知
    static showNotification(message, type = 'info', duration = 3000) {
        const alertClass = type === 'error' ? 'alert-danger' : 
                          type === 'success' ? 'alert-success' : 
                          type === 'warning' ? 'alert-warning' : 'alert-info';
        
        const alertHtml = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        // 如果页面有通知容器，添加到容器中
        const container = document.getElementById('notification-container');
        if (container) {
            container.insertAdjacentHTML('beforeend', alertHtml);
        } else {
            // 否则添加到页面顶部
            document.body.insertAdjacentHTML('afterbegin', `<div class="container mt-3">${alertHtml}</div>`);
        }
        
        // 自动隐藏
        if (duration > 0) {
            setTimeout(() => {
                const alerts = document.querySelectorAll('.alert');
                if (alerts.length > 0) {
                    alerts[alerts.length - 1].remove();
                }
            }, duration);
        }
    }

    // 确认对话框
    static confirm(message, title = '确认') {
        return new Promise((resolve) => {
            if (window.bootstrap && window.bootstrap.Modal) {
                // 使用Bootstrap模态框
                const modalHtml = `
                    <div class="modal fade" id="confirmModal" tabindex="-1">
                        <div class="modal-dialog">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">${title}</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                </div>
                                <div class="modal-body">
                                    <p>${message}</p>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                    <button type="button" class="btn btn-primary" id="confirmBtn">确认</button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
                
                document.body.insertAdjacentHTML('beforeend', modalHtml);
                const modal = new bootstrap.Modal(document.getElementById('confirmModal'));
                
                document.getElementById('confirmBtn').addEventListener('click', () => {
                    modal.hide();
                    resolve(true);
                });
                
                document.getElementById('confirmModal').addEventListener('hidden.bs.modal', () => {
                    document.getElementById('confirmModal').remove();
                    resolve(false);
                });
                
                modal.show();
            } else {
                // 回退到原生确认框
                resolve(confirm(message));
            }
        });
    }

    // 格式化日期
    static formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }

    // 格式化文件大小
    static formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // 防抖函数
    static debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // 节流函数
    static throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    // 验证JSON格式
    static isValidJSON(str) {
        try {
            JSON.parse(str);
            return true;
        } catch (e) {
            return false;
        }
    }

    // 深拷贝对象
    static deepClone(obj) {
        if (obj === null || typeof obj !== 'object') return obj;
        if (obj instanceof Date) return new Date(obj.getTime());
        if (obj instanceof Array) return obj.map(item => Utils.deepClone(item));
        if (typeof obj === 'object') {
            const clonedObj = {};
            for (const key in obj) {
                if (obj.hasOwnProperty(key)) {
                    clonedObj[key] = Utils.deepClone(obj[key]);
                }
            }
            return clonedObj;
        }
    }
}

// 初始化全局实例
window.IntegratedPlatform.toolManager = new ToolManager();
window.IntegratedPlatform.logManager = new LogManager();
window.IntegratedPlatform.utils = Utils;

// 侧边栏管理器
class SidebarManager {
    constructor() {
        this.sidebar = document.getElementById('sidebar');
        this.mobileToggle = document.getElementById('mobileNavToggle');
        this.sidebarToggle = document.getElementById('sidebarToggle');
        this.isCollapsed = false;
        this.isMobile = window.innerWidth <= 768;

        this.init();
    }

    init() {
        // 移动端切换按钮
        if (this.mobileToggle) {
            this.mobileToggle.addEventListener('click', () => {
                this.toggleMobile();
            });
        }

        // 侧边栏关闭按钮
        if (this.sidebarToggle) {
            this.sidebarToggle.addEventListener('click', () => {
                this.toggleMobile();
            });
        }

        // 监听窗口大小变化
        window.addEventListener('resize', Utils.debounce(() => {
            this.handleResize();
        }, 250));

        // 点击外部关闭移动端侧边栏
        document.addEventListener('click', (e) => {
            if (this.isMobile && this.sidebar && this.sidebar.classList.contains('show')) {
                if (!this.sidebar.contains(e.target) && !this.mobileToggle.contains(e.target)) {
                    this.hideMobile();
                }
            }
        });

        // 初始化导航链接激活状态
        this.updateActiveNavLink();
    }

    toggleMobile() {
        if (this.sidebar) {
            this.sidebar.classList.toggle('show');
        }
    }

    showMobile() {
        if (this.sidebar) {
            this.sidebar.classList.add('show');
        }
    }

    hideMobile() {
        if (this.sidebar) {
            this.sidebar.classList.remove('show');
        }
    }

    handleResize() {
        const wasMobile = this.isMobile;
        this.isMobile = window.innerWidth <= 768;

        if (wasMobile && !this.isMobile) {
            // 从移动端切换到桌面端
            this.hideMobile();
        }
    }

    updateActiveNavLink() {
        const currentPath = window.location.pathname;
        const navLinks = document.querySelectorAll('.nav-link');

        navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === currentPath) {
                link.classList.add('active');
            }
        });
    }
}

// 页面动画管理器
class AnimationManager {
    constructor() {
        this.init();
    }

    init() {
        // 添加页面加载动画
        this.addPageLoadAnimation();

        // 添加卡片悬停效果
        this.addCardHoverEffects();

        // 添加按钮点击效果
        this.addButtonClickEffects();
    }

    addPageLoadAnimation() {
        // 为页面元素添加淡入动画
        const elements = document.querySelectorAll('.welcome-section, .dashboard-section, .plugins-section, .architecture-section');

        elements.forEach((element, index) => {
            element.style.opacity = '0';
            element.style.transform = 'translateY(20px)';
            element.style.transition = 'opacity 0.6s ease, transform 0.6s ease';

            setTimeout(() => {
                element.style.opacity = '1';
                element.style.transform = 'translateY(0)';
            }, index * 150);
        });
    }

    addCardHoverEffects() {
        const cards = document.querySelectorAll('.stat-card, .plugin-card, .feature-card');

        cards.forEach(card => {
            card.addEventListener('mouseenter', () => {
                card.style.transform = 'translateY(-4px)';
            });

            card.addEventListener('mouseleave', () => {
                card.style.transform = 'translateY(0)';
            });
        });
    }

    addButtonClickEffects() {
        const buttons = document.querySelectorAll('.btn');

        buttons.forEach(button => {
            button.addEventListener('click', function(e) {
                // 创建波纹效果
                const ripple = document.createElement('span');
                const rect = this.getBoundingClientRect();
                const size = Math.max(rect.width, rect.height);
                const x = e.clientX - rect.left - size / 2;
                const y = e.clientY - rect.top - size / 2;

                ripple.style.width = ripple.style.height = size + 'px';
                ripple.style.left = x + 'px';
                ripple.style.top = y + 'px';
                ripple.classList.add('ripple');

                this.appendChild(ripple);

                setTimeout(() => {
                    ripple.remove();
                }, 600);
            });
        });
    }
}

// DOM加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化侧边栏管理器
    window.IntegratedPlatform.sidebarManager = new SidebarManager();

    // 初始化动画管理器
    window.IntegratedPlatform.animationManager = new AnimationManager();

    // 初始化工具提示
    if (window.bootstrap && window.bootstrap.Tooltip) {
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }

    // 初始化弹出框
    if (window.bootstrap && window.bootstrap.Popover) {
        const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
        popoverTriggerList.map(function (popoverTriggerEl) {
            return new bootstrap.Popover(popoverTriggerEl);
        });
    }

    // 自动隐藏警告框
    setTimeout(() => {
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(alert => {
            if (alert.classList.contains('auto-dismiss')) {
                alert.remove();
            }
        });
    }, 5000);

    // 平滑滚动
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    console.log('集成工具平台初始化完成');
});

// 添加波纹效果的CSS
const rippleStyle = document.createElement('style');
rippleStyle.textContent = `
    .btn {
        position: relative;
        overflow: hidden;
    }

    .ripple {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.3);
        transform: scale(0);
        animation: ripple-animation 0.6s linear;
        pointer-events: none;
    }

    @keyframes ripple-animation {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
`;
document.head.appendChild(rippleStyle);
