/* ==================== 报文回放工具 - 通用样式框架 ==================== */

/* 覆盖基础模板的内容区域限制 */
.content-area {
    max-width: none !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* 确保在所有媒体查询下都覆盖基础模板 */
@media (min-width: 1200px) {
    .content-area {
        max-width: none !important;
        margin: 0 !important;
        padding: 0 !important;
    }
}

/* ==================== 页面容器 ==================== */
.pcap-replay-container {
    width: 100% !important;
    max-width: 1400px !important;
    margin: 0 auto !important;
    padding: 0 24px !important;
    background: transparent;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
    position: relative;
}

/* 确保容器在所有情况下都有一致的宽度 */
.pcap-replay-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: -1;
}

/* ==================== 页面头部 ==================== */
.page-header {
    background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 50%, #ec4899 100%);
    border-radius: 16px;
    margin: 0 -24px 24px -24px;
    box-shadow: 0 8px 32px rgba(79, 70, 229, 0.25);
    overflow: hidden;
    flex-shrink: 0;
    position: relative;
}

/* 添加背景装饰 */
.page-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 24px 32px;
    color: white;
    position: relative;
    z-index: 1;
    min-height: 80px;
}

.header-title {
    display: flex;
    align-items: center;
    gap: 16px;
    flex: 1;
}

.header-title i {
    font-size: 36px;
    opacity: 0.95;
    color: rgba(255, 255, 255, 0.9);
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.header-title h1 {
    font-size: 28px;
    font-weight: 700;
    margin: 0;
    text-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    letter-spacing: -0.5px;
}

.header-subtitle {
    font-size: 15px;
    opacity: 0.9;
    font-weight: 400;
    margin-top: 4px;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-shrink: 0;
}

.action-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 20px;
    background: rgba(255, 255, 255, 0.15);
    border: 2px solid rgba(255, 255, 255, 0.25);
    border-radius: 12px;
    color: white;
    text-decoration: none;
    font-weight: 600;
    font-size: 14px;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(20px);
    min-width: 120px;
    height: 48px;
    text-align: center;
}

.action-btn:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.4);
    transform: translateY(-2px);
    box-shadow: 0 12px 32px rgba(0, 0, 0, 0.2);
    color: white;
}

.action-btn.active {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.action-btn i {
    font-size: 16px;
    flex-shrink: 0;
}

/* ==================== 内容面板容器 ==================== */
.content-panels {
    margin: 0 -24px 24px -24px;
    padding: 24px;
    background: linear-gradient(135deg, rgba(248, 250, 252, 0.95) 0%, rgba(241, 245, 249, 0.9) 100%);
    border-radius: 16px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    flex-shrink: 0;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* ==================== 通用面板样式 ==================== */
.panel {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid #e5e7eb;
    display: flex;
    flex-direction: column;
}

.panel:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 24px rgba(0, 0, 0, 0.12);
}

/* 面板头部 */
.panel-header {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px 20px;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-bottom: 1px solid #e5e7eb;
    flex-shrink: 0;
}

.panel-actions {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-left: auto;
}

/* 自动刷新控件 */
.auto-refresh-control {
    display: flex;
    align-items: center;
}

.auto-refresh-label {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 13px;
    color: #6b7280;
    cursor: pointer;
    user-select: none;
}

.auto-refresh-label input[type="checkbox"] {
    margin: 0;
    cursor: pointer;
}

.refresh-interval {
    font-size: 11px;
    opacity: 0.8;
}

.panel-icon {
    width: 36px;
    height: 36px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 16px;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.panel-title h3 {
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 2px 0;
}

.panel-title p {
    font-size: 12px;
    color: #6b7280;
    margin: 0;
}

/* 面板内容 */
.panel-body {
    padding: 16px 20px;
    flex: 1;
    overflow-y: auto;
    min-height: 0;
}

/* 面板底部 */
.panel-footer {
    padding: 12px 20px;
    background: #f9fafb;
    border-top: 1px solid #e5e7eb;
    flex-shrink: 0;
}

/* ==================== 表单样式 ==================== */
.form-section {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.form-label {
    display: flex;
    align-items: center;
    gap: 6px;
    color: #374151;
    font-size: 13px;
    font-weight: 600;
}

.form-label i {
    color: #667eea;
    width: 14px;
    font-size: 12px;
}

.form-input {
    padding: 8px 12px;
    background: white;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    color: #1f2937;
    font-size: 13px;
    transition: all 0.3s ease;
    font-family: inherit;
}

.form-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-input::placeholder {
    color: #9ca3af;
}

.form-hint {
    color: #6b7280;
    font-size: 11px;
    margin-top: 2px;
}

/* ==================== 按钮样式 ==================== */
.btn-primary {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 8px;
    color: white;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    justify-content: center;
    text-decoration: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    color: white;
}

.btn-primary:disabled {
    background: #9ca3af;
    color: #6b7280;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.btn-primary i {
    font-size: 14px;
}

/* 次要按钮样式 */
.btn-secondary {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: #f8fafc;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    color: #374151;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    justify-content: center;
    text-decoration: none;
}

.btn-secondary:hover {
    background: #f1f5f9;
    border-color: #9ca3af;
    color: #1f2937;
}

.btn-secondary:disabled {
    background: #f3f4f6;
    color: #9ca3af;
    cursor: not-allowed;
    border-color: #e5e7eb;
}

/* 危险按钮样式 */
.btn-danger {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    border: none;
    border-radius: 8px;
    color: white;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    justify-content: center;
    text-decoration: none;
}

.btn-danger:hover {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
    color: white;
}

.btn-danger:disabled {
    background: #9ca3af;
    color: #6b7280;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* 轮廓按钮样式 */
.btn-outline {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 6px 12px;
    background: transparent;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    color: #6b7280;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    justify-content: center;
    text-decoration: none;
}

.btn-outline:hover {
    background: #f9fafb;
    border-color: #9ca3af;
    color: #374151;
}

.btn-outline:disabled {
    background: transparent;
    color: #d1d5db;
    cursor: not-allowed;
    border-color: #e5e7eb;
}

/* 复选框样式 */
.task-checkbox, #selectAllTasks {
    width: 16px;
    height: 16px;
    cursor: pointer;
    accent-color: #667eea;
}

.task-checkbox:disabled {
    cursor: not-allowed;
    opacity: 0.5;
}

/* ==================== 运行日志页面样式 ====================*/

/* 运行日志面板 */
.runtime-logs-panel {
    height: calc(100vh - 200px);
    display: flex;
    flex-direction: column;
}

.runtime-logs-panel .panel-body {
    flex: 1;
    padding: 0;
    overflow: hidden;
}

/* 日志内容区域 */
.logs-content {
    height: 100%;
    display: flex;
    flex-direction: column;
}

.logs-container {
    flex: 1;
    overflow-y: auto;
    padding: 0;
}

/* 日志表格 */
.logs-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 13px;
    background: white;
}

.logs-table th {
    background: #f8fafc;
    padding: 12px 16px;
    text-align: left;
    font-weight: 600;
    color: #374151;
    border-bottom: 1px solid #e5e7eb;
    position: sticky;
    top: 0;
    z-index: 10;
}

.logs-table td {
    padding: 10px 16px;
    border-bottom: 1px solid #f1f5f9;
    vertical-align: top;
}

.logs-table tbody tr:hover {
    background: #f9fafb;
}

/* 日志级别样式 */
.log-level {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
}

.log-level.info {
    background: #dbeafe;
    color: #1e40af;
}

.log-level.warn {
    background: #fef3c7;
    color: #d97706;
}

.log-level.error {
    background: #fee2e2;
    color: #dc2626;
}

.log-level.debug {
    background: #f3f4f6;
    color: #6b7280;
}

/* 日志时间 */
.log-time {
    font-family: 'Courier New', monospace;
    color: #6b7280;
    white-space: nowrap;
    font-size: 12px;
}

/* 日志任务ID */
.log-taskid {
    font-weight: 500;
    color: #4b5563;
    font-size: 11px;
    font-family: 'Courier New', monospace;
    word-break: break-all;
}

/* 日志消息 */
.log-message {
    word-break: break-word;
    line-height: 1.4;
    color: #374151;
}

/* 日志表格列宽 */
.logs-table .col-time {
    width: 180px;
}

.logs-table .col-level {
    width: 80px;
}

.logs-table .col-taskid {
    width: 200px;
    font-family: 'Courier New', monospace;
}

.logs-table .col-message {
    width: auto;
}

/* 加载状态 */
.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px;
    color: #6b7280;
    font-size: 14px;
}

.loading i {
    margin-right: 8px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* 空状态 */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    color: #6b7280;
}

.empty-state i {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
    color: #9ca3af;
}

.empty-state h3 {
    font-size: 18px;
    margin-bottom: 8px;
    color: #374151;
    font-weight: 600;
}

.empty-state p {
    font-size: 14px;
    color: #6b7280;
}

/* ==================== 日志过滤控件样式 ====================*/

/* 过滤控件容器 */
.filter-controls {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-right: 16px;
}

/* 过滤组 */
.filter-group {
    display: flex;
    align-items: center;
    gap: 6px;
}

/* 过滤标签 */
.filter-label {
    font-size: 12px;
    color: #6b7280;
    font-weight: 500;
    white-space: nowrap;
}

/* 过滤选择框 */
.filter-select {
    padding: 4px 8px;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    font-size: 12px;
    color: #374151;
    background: white;
    min-width: 80px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.filter-select:hover {
    border-color: #9ca3af;
}

.filter-select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
}

/* 清除过滤按钮 */
.btn-outline {
    background: transparent;
    border: 1px solid #d1d5db;
    color: #6b7280;
    padding: 4px 8px;
    font-size: 12px;
}

.btn-outline:hover {
    background: #f9fafb;
    border-color: #9ca3af;
    color: #374151;
}

/* 控件分隔线 */
.control-divider {
    width: 1px;
    height: 20px;
    background: #e5e7eb;
    margin: 0 8px;
}

/* 过滤状态指示 */
.filter-active {
    border-color: #667eea !important;
    background: #f0f4ff !important;
}

/* 过滤结果统计 */
.filter-stats {
    font-size: 11px;
    color: #6b7280;
    margin-left: 8px;
    padding: 2px 6px;
    background: #f3f4f6;
    border-radius: 3px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .filter-controls {
        flex-wrap: wrap;
        gap: 8px;
        margin-right: 8px;
    }

    .filter-group {
        gap: 4px;
    }

    .filter-select {
        min-width: 70px;
        font-size: 11px;
    }

    .control-divider {
        display: none;
    }
}

/* ==================== 响应式设计 ==================== */
@media (max-width: 1440px) {
    .pcap-replay-container {
        max-width: 1200px !important;
        padding: 0 24px !important;
        margin: 0 auto !important;
    }

    .page-header {
        margin: 0 -24px 24px -24px;
    }

    .content-panels {
        margin: 0 -24px 24px -24px;
        padding: 24px;
    }
}

@media (max-width: 1024px) {
    .pcap-replay-container {
        max-width: 100% !important;
        padding: 0 20px !important;
        margin: 0 auto !important;
    }

    .page-header {
        margin: 0 -20px 20px -20px;
        border-radius: 12px;
    }

    .content-panels {
        margin: 0 -20px 20px -20px;
        padding: 20px;
        border-radius: 12px;
    }

    .header-content {
        padding: 20px 24px;
        min-height: 72px;
    }

    .header-title h1 {
        font-size: 24px;
    }

    .header-title i {
        font-size: 32px;
    }

    .action-btn {
        min-width: 100px;
        height: 44px;
        padding: 10px 16px;
    }
}

@media (max-width: 768px) {
    .pcap-replay-container {
        max-width: 100% !important;
        padding: 0 16px !important;
    }

    .page-header {
        margin: 0 -16px 20px -16px;
        border-radius: 12px;
    }

    .content-panels {
        margin: 0 -16px 20px -16px;
        padding: 16px;
        border-radius: 12px;
    }

    .header-content {
        flex-direction: column;
        gap: 20px;
        padding: 20px;
        text-align: center;
        min-height: auto;
    }

    .header-title {
        flex-direction: column;
        gap: 12px;
        flex: none;
    }

    .header-title i {
        font-size: 32px;
    }

    .header-title h1 {
        font-size: 22px;
    }

    .header-actions {
        flex-direction: row;
        justify-content: center;
        width: 100%;
        gap: 12px;
    }

    .action-btn {
        flex: 1;
        max-width: 140px;
        min-width: 120px;
        height: 48px;
        justify-content: center;
    }

    .panel-header {
        padding: 16px;
    }

    .panel-body {
        padding: 16px;
    }

    .panel-footer {
        padding: 12px 16px;
    }
}

@media (max-width: 480px) {
    .pcap-replay-container {
        max-width: 100% !important;
        padding: 0 12px !important;
    }

    .page-header {
        margin: 0 -12px 16px -12px;
        border-radius: 12px;
    }

    .content-panels {
        margin: 0 -12px 16px -12px;
        padding: 12px;
        border-radius: 12px;
    }

    .header-content {
        padding: 16px;
        gap: 16px;
    }

    .header-title h1 {
        font-size: 20px;
    }

    .header-title i {
        font-size: 28px;
    }

    .action-btn {
        flex-direction: column;
        gap: 4px;
        min-width: 80px;
        height: 56px;
        padding: 8px 12px;
        font-size: 12px;
    }

    .action-btn i {
        font-size: 14px;
    }

    .panel {
        border-radius: 12px;
    }

    .panel-header {
        padding: 12px;
    }

    .panel-body {
        padding: 12px;
    }

    .panel-footer {
        padding: 10px 12px;
    }
}

/* ==================== 任务结果页面样式 ==================== */

/* 搜索和筛选区域 */
.search-section {
    margin-bottom: 24px;
    padding: 20px;
    background: #f8fafc;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
}

.search-controls {
    display: flex;
    gap: 16px;
    align-items: center;
    flex-wrap: wrap;
}

.search-input-group {
    position: relative;
    flex: 1;
    min-width: 300px;
}

.search-input-group .form-input {
    padding-right: 40px;
}

.search-icon {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #64748b;
    pointer-events: none;
}

.filter-group {
    display: flex;
    gap: 12px;
    align-items: center;
}

.filter-group .form-select {
    min-width: 120px;
}

/* 任务列表区域 */
.task-list-section {
    background: white;
    border-radius: 12px;
    border: 1px solid #e2e8f0;
    overflow: hidden;
}

.task-list-header {
    padding: 16px 20px;
    background: #f8fafc;
    border-bottom: 1px solid #e2e8f0;
}

.list-stats {
    display: flex;
    gap: 24px;
    align-items: center;
}

.stats-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    color: #475569;
}

.stats-item i {
    font-size: 16px;
}

/* 任务表格 */
.task-table-container {
    overflow-x: auto;
}

.task-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.task-table th {
    background: #f8fafc;
    padding: 12px 16px;
    text-align: left;
    font-weight: 600;
    color: #374151;
    border-bottom: 1px solid #e2e8f0;
    white-space: nowrap;
}

.task-table td {
    padding: 12px 16px;
    border-bottom: 1px solid #f1f5f9;
    vertical-align: middle;
}

.task-row:hover {
    background: #f8fafc;
}

/* 表格列宽 */
.col-select {
    width: 40px;
    text-align: center;
}

.col-id {
    width: 120px;
}

.col-name {
    width: 200px;
    min-width: 150px;
}

.col-status {
    width: 100px;
}

.col-result {
    width: 100px;
}

.col-time {
    width: 160px;
}

.col-actions {
    width: 160px;
}

/* 任务ID显示 */
.task-id {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 12px;
    color: #6366f1;
    background: #f0f9ff;
    padding: 2px 6px;
    border-radius: 4px;
    cursor: help;
}

/* 任务名称显示 */
.task-name {
    font-weight: 500;
    color: #1f2937;
    max-width: 180px;
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    vertical-align: middle;
}

/* 状态徽章 */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-running {
    background: #fef3c7;
    color: #d97706;
    border: 1px solid #fbbf24;
}

.status-completed {
    background: #d1fae5;
    color: #059669;
    border: 1px solid #34d399;
}

.status-unknown {
    background: #f3f4f6;
    color: #6b7280;
    border: 1px solid #d1d5db;
}

/* 结果徽章 */
.result-badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.result-success {
    background: #dcfce7;
    color: #16a34a;
    border: 1px solid #4ade80;
}

.result-failed {
    background: #fee2e2;
    color: #dc2626;
    border: 1px solid #f87171;
}

.result-unknown {
    background: #f3f4f6;
    color: #6b7280;
    border: 1px solid #d1d5db;
}

/* 操作按钮 */
.action-buttons {
    display: flex;
    gap: 8px;
    align-items: center;
}

.btn-sm {
    padding: 6px 10px;
    font-size: 12px;
    min-width: auto;
}

.btn-sm i {
    font-size: 12px;
}

/* 加载状态 */
.loading-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 60px 20px;
    color: #6b7280;
}

.loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 12px;
}

.loading-spinner i {
    font-size: 24px;
    color: #6366f1;
}

/* 空状态 */
.empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 60px 20px;
    text-align: center;
}

.empty-icon {
    margin-bottom: 16px;
}

.empty-icon i {
    font-size: 48px;
    color: #d1d5db;
}

.empty-text h4 {
    margin: 0 0 8px 0;
    color: #374151;
    font-size: 18px;
    font-weight: 600;
}

.empty-text p {
    margin: 0 0 20px 0;
    color: #6b7280;
    font-size: 14px;
}

/* 分页 */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    background: #f8fafc;
    border-top: 1px solid #e2e8f0;
}

.pagination-info {
    font-size: 14px;
    color: #6b7280;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 16px;
}

.page-info {
    font-size: 14px;
    color: #374151;
    font-weight: 500;
}

/* 模态框 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

.modal-content {
    background: white;
    border-radius: 12px;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px;
    border-bottom: 1px solid #e2e8f0;
    background: #f8fafc;
}

.modal-header h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #1f2937;
}

.modal-close {
    background: none;
    border: none;
    font-size: 18px;
    color: #6b7280;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.modal-close:hover {
    background: #f3f4f6;
    color: #374151;
}

.modal-body {
    padding: 24px;
    overflow-y: auto;
}

/* 任务详情 */
.task-detail {
    font-size: 14px;
}

.detail-section {
    margin-bottom: 24px;
}

.detail-section:last-child {
    margin-bottom: 0;
}

.detail-section h4 {
    margin: 0 0 16px 0;
    font-size: 16px;
    font-weight: 600;
    color: #1f2937;
    border-bottom: 1px solid #e2e8f0;
    padding-bottom: 8px;
}

.detail-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
}

.detail-item {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.detail-item label {
    font-weight: 500;
    color: #6b7280;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.detail-item span {
    color: #1f2937;
    word-break: break-all;
}

/* ==================== 任务结果页面响应式设计 ==================== */

@media (max-width: 768px) {
    /* 搜索控件在小屏幕上垂直排列 */
    .search-controls {
        flex-direction: column;
        align-items: stretch;
    }

    .search-input-group {
        min-width: auto;
    }

    .filter-group {
        justify-content: space-between;
    }

    .filter-group .form-select {
        flex: 1;
        min-width: auto;
    }

    /* 统计信息在小屏幕上换行 */
    .list-stats {
        flex-wrap: wrap;
        gap: 16px;
    }

    /* 表格在小屏幕上隐藏部分列 */
    .task-table .col-time {
        display: none;
    }

    .task-table .col-id {
        width: 80px;
    }

    .task-table .col-name {
        width: auto;
        min-width: 120px;
    }

    /* 分页控件调整 */
    .pagination-container {
        flex-direction: column;
        gap: 12px;
        text-align: center;
    }

    .pagination-controls {
        justify-content: center;
    }

    /* 模态框在小屏幕上全屏显示 */
    .modal-content {
        width: 95%;
        max-height: 90vh;
    }

    .detail-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    /* 更小屏幕的优化 */
    .pcap-replay-container {
        padding: 0 16px !important;
    }

    .page-header {
        margin: 0 -16px 16px -16px;
    }

    .search-section {
        padding: 16px;
        margin-bottom: 16px;
    }

    /* 表格进一步简化 */
    .task-table .col-status,
    .task-table .col-result {
        display: none;
    }

    .task-table td {
        padding: 8px 12px;
    }

    /* 操作按钮调整 */
    .action-buttons {
        flex-direction: column;
        gap: 4px;
    }

    .btn-sm {
        width: 100%;
        justify-content: center;
    }

    /* 空状态调整 */
    .empty-state {
        padding: 40px 16px;
    }

    .empty-icon i {
        font-size: 36px;
    }

    .empty-text h4 {
        font-size: 16px;
    }
}
