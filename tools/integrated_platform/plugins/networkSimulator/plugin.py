# -*- coding: utf-8 -*-
"""
网络模拟访问插件主类
"""

import os
import json
import uuid
import threading
import zipfile

import json
from datetime import datetime
from typing import List, Dict, Any, Optional
from flask import render_template, request, jsonify, send_file, current_app

from core.base_plugin import BasePlugin
from .network_simulator import NetworkSimulator


class Plugin(BasePlugin):
    """网络模拟访问插件"""

    def __init__(self, **kwargs):
        # 先调用父类初始化
        super().__init__(**kwargs)
        
        # 设置插件信息
        self.name = "networkSimulator"
        self.displayName = "网络模拟访问"
        self.description = "支持模拟客户端访问IP和域名，生成网络报文抓包文件"
        self.version = "1.0.0"
        self.author = "Plugin Developer"
        
        # 初始化插件目录
        self.pluginDir = os.path.dirname(os.path.abspath(__file__))
        self.uploadsDir = os.path.join(self.pluginDir, 'uploads')
        self.reportsDir = os.path.join(self.pluginDir, 'reports')
        self.logsDir = os.path.join(self.pluginDir, 'logs')
        
        # 确保目录存在
        for directory in [self.uploadsDir, self.reportsDir, self.logsDir]:
            if not os.path.exists(directory):
                os.makedirs(directory)
        
        # 任务存储
        self.tasks = {}
        self.taskLock = threading.Lock()
        self.tasksFile = os.path.join(self.pluginDir, 'data', 'tasks.json')
        self._ensureDataDir()
        self._loadTasks()
        
        # 网络模拟器实例
        self.networkSimulator = NetworkSimulator(self.pluginDir, self.logger)

    def _ensureDataDir(self):
        """确保数据目录存在"""
        dataDir = os.path.join(self.pluginDir, 'data')
        if not os.path.exists(dataDir):
            os.makedirs(dataDir)

    def _loadTasks(self):
        """从文件加载任务数据"""
        try:
            if os.path.exists(self.tasksFile):
                with open(self.tasksFile, 'r', encoding='utf-8') as f:
                    self.tasks = json.load(f)
                self.logger.info(f"加载了 {len(self.tasks)} 个任务")
        except Exception as e:
            self.logger.error(f"加载任务文件失败: {str(e)}")
            self.tasks = {}

    def _saveTasks(self):
        """保存任务数据到文件"""
        try:
            with open(self.tasksFile, 'w', encoding='utf-8') as f:
                json.dump(self.tasks, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.logger.error(f"保存任务文件失败: {str(e)}")

    def _hasReportFiles(self, reportDir: str) -> bool:
        """检查报告目录中是否有pcap文件"""
        if not reportDir or not os.path.exists(reportDir):
            return False

        # 检查目录中是否有pcap文件
        for filename in os.listdir(reportDir):
            if filename.endswith('.pcap'):
                return True

        return False

    def getRoutes(self) -> List[Dict[str, Any]]:
        """获取页面路由配置"""
        return [
            {
                "rule": "/",
                "view_func": self.createTaskPage,
                "methods": ["GET"],
                "endpoint": "index"
            },
            {
                "rule": "/create-task",
                "view_func": self.createTaskPage,
                "methods": ["GET"],
                "endpoint": "create_task_page"
            },
            {
                "rule": "/task-results",
                "view_func": self.taskResultsPage,
                "methods": ["GET"],
                "endpoint": "task_results_page"
            }
        ]

    def getApiRoutes(self) -> List[Dict[str, Any]]:
        """获取API路由配置"""
        return [
            # 任务管理API
            {
                "rule": "/tasks",
                "view_func": self.getTasks,
                "methods": ["GET"],
                "endpoint": "get_tasks"
            },
            {
                "rule": "/tasks",
                "view_func": self.createTask,
                "methods": ["POST"],
                "endpoint": "create_task"
            },
            {
                "rule": "/tasks/<task_id>/status",
                "view_func": self.getTaskStatus,
                "methods": ["GET"],
                "endpoint": "get_task_status"
            },
            {
                "rule": "/file-tasks",
                "view_func": self.createFileTask,
                "methods": ["POST"],
                "endpoint": "create_file_task"
            },

            {
                "rule": "/tasks/<task_id>/download-report",
                "view_func": self.downloadTaskReport,
                "methods": ["GET"],
                "endpoint": "download_task_report"
            },
            {
                "rule": "/tasks/<task_id>/delete",
                "view_func": self.deleteTask,
                "methods": ["DELETE"],
                "endpoint": "delete_task"
            },
            {
                "rule": "/tasks/batch-delete",
                "view_func": self.batchDeleteTasks,
                "methods": ["POST"],
                "endpoint": "batch_delete_tasks"
            }
        ]

    def getNavItems(self) -> List[Dict[str, Any]]:
        """获取导航项配置"""
        return [
            {
                "title": "网络模拟访问",
                "url": "/plugins/networkSimulator/",
                "icon": "fas fa-network-wired",
                "order": 30
            }
        ]

    def onInitialize(self):
        """插件初始化回调"""
        self.logger.info("网络模拟访问插件初始化完成")
        
        # 初始化网络模拟器
        self.networkSimulator = NetworkSimulator(
            plugin_dir=self.pluginDir,
            logger=self.logger
        )



    def createTaskPage(self):
        """创建任务页面"""
        try:
            return render_template('networkSimulator/create_task.html',
                                 pluginInfo=self.getInfo())
        except Exception as e:
            self.logger.error(f"加载创建任务页面失败: {str(e)}")
            return f"加载页面失败: {str(e)}", 500

    def taskResultsPage(self):
        """任务结果页面"""
        try:
            return render_template('networkSimulator/task_results.html',
                                 pluginInfo=self.getInfo())
        except Exception as e:
            self.logger.error(f"加载任务结果页面失败: {str(e)}")
            return f"加载页面失败: {str(e)}", 500

    def getTasks(self):
        """获取任务列表"""
        try:
            with self.taskLock:
                taskList = []
                for taskId, task in self.tasks.items():
                    taskList.append({
                        'id': taskId,
                        'name': task.get('name', ''),
                        'status': task.get('status', 'unknown'),
                        'createTime': task.get('createTime', ''),
                        'finishTime': task.get('finishTime', ''),
                        'result': task.get('result', ''),
                        'hasReport': self._hasReportFiles(task.get('reportDir', ''))
                    })

                # 按创建时间倒序排序（最新的任务在前面）
                taskList.sort(key=lambda x: x.get('createTime', ''), reverse=True)

                return jsonify({
                    'success': True,
                    'data': taskList
                })
        except Exception as e:
            self.logger.error(f"获取任务列表失败: {str(e)}")
            return jsonify({
                'success': False,
                'message': f"获取任务列表失败: {str(e)}"
            }), 500

    def createTask(self):
        """创建网络模拟任务"""
        try:
            data = request.get_json()
            if not data:
                return jsonify({
                    'success': False,
                    'message': '请求数据为空'
                }), 400
            
            # 验证必需参数
            taskName = data.get('taskName', '').strip()
            targets = data.get('targets', '').strip()
            protocol = data.get('protocol', 'http')
            port = data.get('port', 80)
            
            if not taskName:
                return jsonify({
                    'success': False,
                    'message': '任务名称不能为空'
                }), 400
            
            if not targets:
                return jsonify({
                    'success': False,
                    'message': '目标地址不能为空'
                }), 400
            
            # 生成任务ID
            taskId = str(uuid.uuid4())
            
            # 解析目标列表
            targetList = [target.strip() for target in targets.split('\n') if target.strip()]
            
            # 创建任务记录
            task = {
                'id': taskId,
                'name': taskName,
                'targets': targetList,
                'protocol': protocol,
                'port': int(port),
                'status': 'pending',
                'createTime': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'finishTime': '',
                'result': '',
                'reportDir': os.path.join(self.reportsDir, taskId)
            }
            
            with self.taskLock:
                self.tasks[taskId] = task
                self._saveTasks()
            
            # 启动后台任务
            thread = threading.Thread(
                target=self._executeTask,
                args=(taskId,),
                daemon=True
            )
            thread.start()
            
            return jsonify({
                'success': True,
                'message': '任务创建成功',
                'data': {'taskId': taskId}
            })
            
        except Exception as e:
            self.logger.error(f"创建任务失败: {str(e)}")
            return jsonify({
                'success': False,
                'message': f"创建任务失败: {str(e)}"
            }), 500

    def getTaskStatus(self, task_id):
        """获取任务状态"""
        try:
            with self.taskLock:
                task = self.tasks.get(task_id)
                if not task:
                    return jsonify({
                        'success': False,
                        'message': '任务不存在'
                    }), 404
                
                return jsonify({
                    'success': True,
                    'data': {
                        'id': task['id'],
                        'name': task['name'],
                        'status': task['status'],
                        'result': task['result'],
                        'hasReport': self._hasReportFiles(task.get('reportDir', ''))
                    }
                })
        except Exception as e:
            self.logger.error(f"获取任务状态失败: {str(e)}")
            return jsonify({
                'success': False,
                'message': f"获取任务状态失败: {str(e)}"
            }), 500



    def downloadTaskReport(self, task_id):
        """下载任务报文压缩包"""
        try:
            with self.taskLock:
                task = self.tasks.get(task_id)
                if not task:
                    return jsonify({
                        'success': False,
                        'message': '任务不存在'
                    }), 404

                reportDir = task.get('reportDir', '')

                # 创建压缩包
                zipPath = self._createTaskReportZip(task_id, task, reportDir)
                if not zipPath:
                    return jsonify({
                        'success': False,
                        'message': '生成压缩包失败'
                    }), 500

                return send_file(
                    zipPath,
                    as_attachment=True,
                    download_name=f"simulation_report_{task_id}.zip"
                )
        except Exception as e:
            self.logger.error(f"下载任务报文压缩包失败: {str(e)}")
            return jsonify({
                'success': False,
                'message': f"下载失败: {str(e)}"
            }), 500

    def deleteTask(self, task_id):
        """删除任务"""
        try:
            with self.taskLock:
                task = self.tasks.get(task_id)
                if not task:
                    return jsonify({
                        'success': False,
                        'message': '任务不存在'
                    }), 404
                
                # 删除报告目录
                reportDir = task.get('reportDir', '')
                if os.path.exists(reportDir):
                    import shutil
                    shutil.rmtree(reportDir)
                
                #删除压缩包报告
                zipFile=os.path.join(reportDir, f'simulation_report_{task_id}.zip')
                if os.path.exists(zipFile):
                    os.remove(zipFile)
                
                # 删除任务记录
                del self.tasks[task_id]
                
                return jsonify({
                    'success': True,
                    'message': '任务删除成功'
                })
        except Exception as e:
            self.logger.error(f"删除任务失败: {str(e)}")
            return jsonify({
                'success': False,
                'message': f"删除任务失败: {str(e)}"
            }), 500

    def batchDeleteTasks(self):
        """批量删除任务"""
        try:
            # 获取请求数据
            data = request.get_json()
            if not data or 'taskIds' not in data:
                return jsonify({
                    'success': False,
                    'message': '请求参数错误'
                }), 400

            taskIds = data['taskIds']
            if not isinstance(taskIds, list) or len(taskIds) == 0:
                return jsonify({
                    'success': False,
                    'message': '任务ID列表不能为空'
                }), 400

            deletedCount = 0
            failedTasks = []

            with self.taskLock:
                for taskId in taskIds:
                    try:
                        task = self.tasks.get(taskId)
                        if not task:
                            failedTasks.append(f"任务 {taskId} 不存在")
                            continue

                        # 删除报告目录
                        reportDir = task.get('reportDir', '')
                        if os.path.exists(reportDir):
                            import shutil
                            shutil.rmtree(reportDir)

                        # 删除压缩包报告
                        zipFile = os.path.join(reportDir, f'simulation_report_{taskId}.zip')
                        if os.path.exists(zipFile):
                            os.remove(zipFile)

                        # 删除任务记录
                        del self.tasks[taskId]
                        deletedCount += 1

                    except Exception as e:
                        failedTasks.append(f"删除任务 {taskId} 失败: {str(e)}")
                        self.logger.error(f"删除任务 {taskId} 失败: {str(e)}")

                # 保存任务数据
                self._saveTasks()

            # 构建响应消息
            if deletedCount == len(taskIds):
                message = f"成功删除 {deletedCount} 个任务"
            elif deletedCount > 0:
                message = f"成功删除 {deletedCount} 个任务，{len(failedTasks)} 个任务删除失败"
            else:
                message = "所有任务删除失败"

            return jsonify({
                'success': deletedCount > 0,
                'message': message,
                'data': {
                    'deletedCount': deletedCount,
                    'failedCount': len(failedTasks),
                    'failedTasks': failedTasks
                }
            })

        except Exception as e:
            self.logger.error(f"批量删除任务失败: {str(e)}")
            return jsonify({
                'success': False,
                'message': f"批量删除任务失败: {str(e)}"
            }), 500

    def createFileTask(self):
        """创建文件传输任务"""
        try:
            # 获取表单数据
            taskName = request.form.get('taskName', '').strip()
            protocol = request.form.get('protocol', 'http')
            port = request.form.get('port', '80')
            targetIp = request.form.get('targetIp', '127.0.0.1').strip()
            targetDomain = request.form.get('targetDomain', 'testfileserver').strip()

            # 验证参数
            if not taskName:
                return jsonify({
                    'success': False,
                    'message': '任务名称不能为空'
                }), 400

            # 获取上传的文件
            files = request.files.getlist('files')
            if not files or len(files) == 0:
                return jsonify({
                    'success': False,
                    'message': '请选择要上传的文件'
                }), 400

            # 生成任务ID
            taskId = str(uuid.uuid4())

            # 创建任务目录
            taskDir = os.path.join(self.reportsDir, taskId)
            os.makedirs(taskDir, exist_ok=True)

            # 保存上传的文件
            fileList = []
            for file in files:
                if file.filename:
                    filename = file.filename
                    filepath = os.path.join(taskDir, filename)
                    file.save(filepath)
                    fileList.append({
                        'name': filename,
                        'path': filepath,
                        'size': os.path.getsize(filepath)
                    })

            # 创建任务记录
            task = {
                'id': taskId,
                'name': taskName,
                'files': fileList,
                'protocol': protocol,
                'port': int(port),
                'targetIp': targetIp,
                'targetDomain': targetDomain,
                'status': 'pending',
                'createTime': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'finishTime': '',
                'result': '',
                'reportDir': taskDir,
                'taskType': 'file'
            }

            with self.taskLock:
                self.tasks[taskId] = task
                self._saveTasks()

            # 启动任务执行线程
            taskThread = threading.Thread(target=self._executeFileTask, args=(taskId,))
            taskThread.daemon = True
            taskThread.start()

            return jsonify({
                'success': True,
                'message': '文件传输任务创建成功',
                'data': {
                    'taskId': taskId,
                    'taskName': taskName,
                    'fileCount': len(fileList)
                }
            })

        except Exception as e:
            self.logger.error(f"创建文件任务失败: {str(e)}")
            return jsonify({
                'success': False,
                'message': f"创建任务失败: {str(e)}"
            }), 500

    def _executeTask(self, task_id):
        """执行网络模拟任务"""
        try:
            with self.taskLock:
                task = self.tasks.get(task_id)
                if not task:
                    return

                task['status'] = 'running'
                self._saveTasks()
            
            # 确保报告目录存在
            os.makedirs(task['reportDir'], exist_ok=True)

            # 执行网络模拟
            result = self.networkSimulator.simulateNetworkAccess(
                targets=task['targets'],
                protocol=task['protocol'],
                port=task['port'],
                output_dir=task['reportDir']
            )
            
            with self.taskLock:
                task['status'] = 'completed' if result['success'] else 'failed'
                task['result'] = result['message']
                task['finishTime'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                self._saveTasks()
            
        except Exception as e:
            self.logger.error(f"执行任务失败: {str(e)}")
            with self.taskLock:
                if task_id in self.tasks:
                    self.tasks[task_id]['status'] = 'failed'
                    self.tasks[task_id]['result'] = f"执行失败: {str(e)}"
                    self.tasks[task_id]['finishTime'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    self._saveTasks()

    def _executeFileTask(self, task_id: str):
        """执行文件传输任务"""
        try:
            with self.taskLock:
                task = self.tasks.get(task_id)
                if not task:
                    return

                task['status'] = 'running'
                self._saveTasks()

            # 确保报告目录存在
            os.makedirs(task['reportDir'], exist_ok=True)

            # 执行文件传输模拟
            result = self.networkSimulator.simulateFileTransfer(
                files=task['files'],
                protocol=task['protocol'],
                port=task['port'],
                output_dir=task['reportDir'],
                target_ip=task.get('targetIp', '127.0.0.1'),
                target_domain=task.get('targetDomain', 'testfileserver')
            )

            with self.taskLock:
                task['status'] = 'completed' if result['success'] else 'failed'
                task['result'] = result['message']
                task['finishTime'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                self._saveTasks()

        except Exception as e:
            self.logger.error(f"执行文件任务失败: {str(e)}")
            with self.taskLock:
                if task_id in self.tasks:
                    self.tasks[task_id]['status'] = 'failed'
                    self.tasks[task_id]['result'] = f"执行失败: {str(e)}"
                    self.tasks[task_id]['finishTime'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    self._saveTasks()

    def _createTaskReportZip(self, task_id: str, task: Dict[str, Any], reportDir: str) -> str:
        """创建任务报文压缩包"""
        try:
            # 创建临时压缩文件
            zipPath = os.path.join(self.reportsDir, f"simulation_report_{task_id}.zip")

            with zipfile.ZipFile(zipPath, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # 添加所有pcap文件
                if os.path.exists(reportDir):
                    for filename in os.listdir(reportDir):
                        if filename.endswith('.pcap'):
                            filePath = os.path.join(reportDir, filename)
                            if os.path.exists(filePath):
                                zipf.write(filePath, filename)

                # 只包含pcap文件，不添加其他文件

            self.logger.info(f"成功创建任务报文压缩包: {zipPath}")
            return zipPath

        except Exception as e:
            self.logger.error(f"创建任务报文压缩包失败: {str(e)}")
            return None

    def getInfo(self) -> Dict[str, Any]:
        """获取插件信息"""
        return {
            'name': self.name,
            'displayName': self.displayName,
            'description': self.description,
            'version': self.version,
            'author': self.author
        }
