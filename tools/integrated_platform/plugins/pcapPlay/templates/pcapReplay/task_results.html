{% extends "base.html" %}

{% block title %}任务结果查询 - {{ pluginInfo.displayName }}{% endblock %}

{% block content %}
<div class="pcap-replay-container">
    <!-- 页面头部 -->
    <div class="page-header">
        <div class="header-content">
            <div class="header-title">
                <i class="fas fa-play-circle me-3"></i>
            </div>
            <div class="header-actions">
                <a class="action-btn" href="/plugins/pcapPlay/">
                    <i class="fas fa-cog"></i>
                    <span>系统设置</span>
                </a>
                <a class="action-btn" href="/plugins/pcapPlay/create-task">
                    <i class="fas fa-plus-circle"></i>
                    <span>新建任务</span>
                </a>
                <a class="action-btn active" href="/plugins/pcapPlay/task-results">
                    <i class="fas fa-list-alt"></i>
                    <span>任务结果</span>
                </a>
                <a class="action-btn" href="/plugins/pcapPlay/runtime-logs">
                    <i class="fas fa-file-alt"></i>
                    <span>运行日志</span>
                </a>
            </div>
        </div>
    </div>

    <!-- 任务结果面板 -->
    <div class="content-panels">
        <div class="panel task-results-panel">
            <div class="panel-header">
                <div class="panel-icon">
                    <i class="fas fa-list-alt"></i>
                </div>
                <div class="panel-title">
                    <h3>任务结果查询</h3>
                    <p>查看所有测试任务的执行结果和报告</p>
                </div>
                <div class="panel-actions">
                    <div class="auto-refresh-control">
                        <label class="auto-refresh-label">
                            <input type="checkbox" id="autoRefreshToggle" onchange="toggleAutoRefresh()">
                            <span>自动刷新</span>
                            <span class="refresh-interval">(30秒)</span>
                        </label>
                    </div>
                    <button class="btn btn-secondary" onclick="refreshTaskList()">
                        <i class="fas fa-sync-alt"></i>
                        <span>刷新</span>
                    </button>
                    <button class="btn btn-danger" id="batchDeleteBtn" onclick="batchDeleteTasks()" disabled>
                        <i class="fas fa-trash-alt"></i>
                        <span>批量删除</span>
                    </button>
                </div>
            </div>
            <div class="panel-body">
                <!-- 搜索和筛选 -->
                <div class="search-section">
                    <div class="search-controls">
                        <div class="search-input-group">
                            <input type="text" 
                                   id="searchInput" 
                                   class="form-input" 
                                   placeholder="搜索任务名称或ID..."
                                   onkeyup="filterTasks()">
                            <i class="fas fa-search search-icon"></i>
                        </div>
                        <div class="filter-group">
                            <select id="statusFilter" class="form-select" onchange="filterTasks()">
                                <option value="">全部状态</option>
                                <option value="进行中">进行中</option>
                                <option value="已完成">已完成</option>
                            </select>
                            <select id="resultFilter" class="form-select" onchange="filterTasks()">
                                <option value="">全部结果</option>
                                <option value="成功">成功</option>
                                <option value="失败">失败</option>
                                <option value="未知">未知</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- 任务列表 -->
                <div class="task-list-section">
                    <div class="task-list-header">
                        <div class="list-stats">
                            <span class="stats-item">
                                <i class="fas fa-tasks"></i>
                                <span>总任务数: <span id="totalTasks">0</span></span>
                            </span>
                            <span class="stats-item">
                                <i class="fas fa-check-circle text-success"></i>
                                <span>成功: <span id="successTasks">0</span></span>
                            </span>
                            <span class="stats-item">
                                <i class="fas fa-times-circle text-danger"></i>
                                <span>失败: <span id="failedTasks">0</span></span>
                            </span>
                        </div>
                    </div>

                    <!-- 任务表格 -->
                    <div class="task-table-container">
                        <table class="task-table" id="taskTable">
                            <thead>
                                <tr>
                                    <th class="col-select">
                                        <input type="checkbox" id="selectAllTasks" onchange="toggleSelectAll()">
                                    </th>
                                    <th class="col-id">任务ID</th>
                                    <th class="col-name">任务名称</th>
                                    <th class="col-status">状态</th>
                                    <th class="col-result">结果</th>
                                    <th class="col-time">创建时间</th>
                                    <th class="col-actions">操作</th>
                                </tr>
                            </thead>
                            <tbody id="taskTableBody">
                                <!-- 任务数据将通过JavaScript动态加载 -->
                            </tbody>
                        </table>
                    </div>

                    <!-- 加载状态 -->
                    <div class="loading-container" id="loadingContainer">
                        <div class="loading-spinner">
                            <i class="fas fa-spinner fa-spin"></i>
                            <span>加载任务列表中...</span>
                        </div>
                    </div>

                    <!-- 空状态 -->
                    <div class="empty-state" id="emptyState" style="display: none;">
                        <div class="empty-icon">
                            <i class="fas fa-inbox"></i>
                        </div>
                        <div class="empty-text">
                            <h4>暂无任务记录</h4>
                            <p>还没有创建任何测试任务</p>
                            <a href="/plugins/pcapPlay/create-task" class="btn btn-primary">
                                <i class="fas fa-plus"></i>
                                <span>创建第一个任务</span>
                            </a>
                        </div>
                    </div>

                    <!-- 分页 -->
                    <div class="pagination-container" id="paginationContainer">
                        <div class="pagination-info">
                            <span>显示 <span id="pageStart">1</span> - <span id="pageEnd">10</span> 条，共 <span id="totalCount">0</span> 条</span>
                        </div>
                        <div class="pagination-controls">
                            <button class="btn btn-outline" id="prevPage" onclick="changePage(-1)">
                                <i class="fas fa-chevron-left"></i>
                                <span>上一页</span>
                            </button>
                            <span class="page-info">
                                第 <span id="currentPage">1</span> 页，共 <span id="totalPages">1</span> 页
                            </span>
                            <button class="btn btn-outline" id="nextPage" onclick="changePage(1)">
                                <span>下一页</span>
                                <i class="fas fa-chevron-right"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 任务详情模态框 -->
<div class="modal" id="taskDetailModal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>任务详情</h3>
            <button class="modal-close" onclick="closeTaskDetail()">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="modal-body" id="taskDetailContent">
            <!-- 任务详情内容将通过JavaScript动态加载 -->
        </div>
    </div>
</div>

<script>
// 全局变量
let currentPage = 1;
let pageSize = 10;
let totalTasks = 0;
let allTasks = [];
let filteredTasks = [];
let autoRefreshInterval = null;
let autoRefreshEnabled = false;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadTaskList();
});

// 页面卸载时清理自动刷新
window.addEventListener('beforeunload', function() {
    stopAutoRefresh();
});

// 加载任务列表
function loadTaskList() {
    showLoading(true);
    
    fetch('/api/plugins/pcapPlay/tasks')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                allTasks = data.data.tasks || [];
                filteredTasks = [...allTasks];
                updateStats();
                renderTaskTable();
                updatePagination();
            } else {
                showError('加载任务列表失败: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error loading tasks:', error);
            showError('加载任务列表失败: ' + error.message);
        })
        .finally(() => {
            showLoading(false);
        });
}

// 显示/隐藏加载状态
function showLoading(show) {
    const loadingContainer = document.getElementById('loadingContainer');
    const taskTable = document.querySelector('.task-table-container');
    const emptyState = document.getElementById('emptyState');
    
    if (show) {
        loadingContainer.style.display = 'flex';
        taskTable.style.display = 'none';
        emptyState.style.display = 'none';
    } else {
        loadingContainer.style.display = 'none';
        if (filteredTasks.length === 0) {
            emptyState.style.display = 'flex';
            taskTable.style.display = 'none';
        } else {
            emptyState.style.display = 'none';
            taskTable.style.display = 'block';
        }
    }
}

// 更新统计信息
function updateStats() {
    const total = allTasks.length;
    const success = allTasks.filter(task => task.result === '成功').length;
    const failed = allTasks.filter(task => task.result === '失败').length;
    
    document.getElementById('totalTasks').textContent = total;
    document.getElementById('successTasks').textContent = success;
    document.getElementById('failedTasks').textContent = failed;
}

// 渲染任务表格
function renderTaskTable() {
    const tbody = document.getElementById('taskTableBody');
    const startIndex = (currentPage - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    const tasksToShow = filteredTasks.slice(startIndex, endIndex);

    // 保存当前选中状态
    const selectedTasks = getSelectedTasks();

    tbody.innerHTML = '';

    tasksToShow.forEach(task => {
        const row = createTaskRow(task);
        tbody.appendChild(row);

        // 恢复选中状态
        const checkbox = row.querySelector('.task-checkbox');
        if (checkbox && selectedTasks.includes(task.id)) {
            checkbox.checked = true;
        }
    });

    // 更新批量删除按钮状态
    updateBatchDeleteButton();
}

// 创建任务行
function createTaskRow(task) {
    const row = document.createElement('tr');
    row.className = 'task-row';

    // 格式化时间
    const createTime = task.createTime ? new Date(task.createTime).toLocaleString() : '未知';

    // 状态样式
    const statusClass = getStatusClass(task.status);
    const resultClass = getResultClass(task.result);

    row.innerHTML = `
        <td class="col-select">
            <input type="checkbox" class="task-checkbox" value="${task.id}" onchange="updateBatchDeleteButton()">
        </td>
        <td class="col-id">
            <span class="task-id" title="${task.id}">${task.id.substring(0, 8)}...</span>
        </td>
        <td class="col-name">
            <span class="task-name" title="${task.name}">${task.name}</span>
        </td>
        <td class="col-status">
            <span class="status-badge ${statusClass}">${task.status}</span>
        </td>
        <td class="col-result">
            <span class="result-badge ${resultClass}">${task.result}</span>
        </td>
        <td class="col-time">
            <span class="create-time">${createTime}</span>
        </td>
        <td class="col-actions">
            <div class="action-buttons">
                <button class="btn btn-sm btn-outline" onclick="viewTaskDetail('${task.id}')" title="查看详情">
                    <i class="fas fa-eye"></i>
                </button>
                ${task.reportPath ? `
                    <button class="btn btn-sm btn-primary" onclick="downloadReport('${task.id}')" title="下载报告">
                        <i class="fas fa-download"></i>
                    </button>
                ` : `
                    <button class="btn btn-sm btn-outline" disabled title="无报告">
                        <i class="fas fa-file-alt"></i>
                    </button>
                `}
                <button class="btn btn-sm btn-danger" onclick="deleteTask('${task.id}')" title="${task.status === '进行中' ? '强制删除任务' : '删除任务'}">
                    <i class="fas fa-trash-alt"></i>
                </button>
            </div>
        </td>
    `;

    return row;
}

// 获取状态样式类
function getStatusClass(status) {
    switch (status) {
        case '进行中':
            return 'status-running';
        case '已完成':
            return 'status-completed';
        default:
            return 'status-unknown';
    }
}

// 获取结果样式类
function getResultClass(result) {
    switch (result) {
        case '成功':
            return 'result-success';
        case '失败':
            return 'result-failed';
        default:
            return 'result-unknown';
    }
}

// 筛选任务
function filterTasks() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase();
    const statusFilter = document.getElementById('statusFilter').value;
    const resultFilter = document.getElementById('resultFilter').value;

    filteredTasks = allTasks.filter(task => {
        const matchesSearch = !searchTerm ||
            task.name.toLowerCase().includes(searchTerm) ||
            task.id.toLowerCase().includes(searchTerm);

        const matchesStatus = !statusFilter || task.status === statusFilter;
        const matchesResult = !resultFilter || task.result === resultFilter;

        return matchesSearch && matchesStatus && matchesResult;
    });

    currentPage = 1;
    renderTaskTable();
    updatePagination();
}

// 更新分页
function updatePagination() {
    const totalCount = filteredTasks.length;
    const totalPages = Math.ceil(totalCount / pageSize);
    const startIndex = (currentPage - 1) * pageSize + 1;
    const endIndex = Math.min(currentPage * pageSize, totalCount);

    document.getElementById('totalCount').textContent = totalCount;
    document.getElementById('totalPages').textContent = totalPages;
    document.getElementById('currentPage').textContent = currentPage;
    document.getElementById('pageStart').textContent = totalCount > 0 ? startIndex : 0;
    document.getElementById('pageEnd').textContent = totalCount > 0 ? endIndex : 0;

    // 更新按钮状态
    document.getElementById('prevPage').disabled = currentPage <= 1;
    document.getElementById('nextPage').disabled = currentPage >= totalPages;

    // 显示/隐藏分页
    const paginationContainer = document.getElementById('paginationContainer');
    paginationContainer.style.display = totalCount > 0 ? 'flex' : 'none';
}

// 切换页面
function changePage(direction) {
    const totalPages = Math.ceil(filteredTasks.length / pageSize);
    const newPage = currentPage + direction;

    if (newPage >= 1 && newPage <= totalPages) {
        currentPage = newPage;
        renderTaskTable();
        updatePagination();
    }
}

// 刷新任务列表
function refreshTaskList() {
    loadTaskList();
}

// 查看任务详情
function viewTaskDetail(taskId) {
    const task = allTasks.find(t => t.id === taskId);
    if (!task) {
        showError('任务不存在');
        return;
    }

    const modal = document.getElementById('taskDetailModal');
    const content = document.getElementById('taskDetailContent');

    content.innerHTML = `
        <div class="task-detail">
            <div class="detail-section">
                <h4>基本信息</h4>
                <div class="detail-grid">
                    <div class="detail-item">
                        <label>任务ID:</label>
                        <span>${task.id}</span>
                    </div>
                    <div class="detail-item">
                        <label>任务名称:</label>
                        <span>${task.name}</span>
                    </div>
                    <div class="detail-item">
                        <label>状态:</label>
                        <span class="status-badge ${getStatusClass(task.status)}">${task.status}</span>
                    </div>
                    <div class="detail-item">
                        <label>结果:</label>
                        <span class="result-badge ${getResultClass(task.result)}">${task.result}</span>
                    </div>
                    <div class="detail-item">
                        <label>创建时间:</label>
                        <span>${task.createTime ? new Date(task.createTime).toLocaleString() : '未知'}</span>
                    </div>
                    <div class="detail-item">
                        <label>报告路径:</label>
                        <span>${task.reportPath || '无'}</span>
                    </div>
                </div>
            </div>
        </div>
    `;

    modal.style.display = 'flex';
}

// 关闭任务详情
function closeTaskDetail() {
    document.getElementById('taskDetailModal').style.display = 'none';
}

// 下载报告
function downloadReport(taskId) {
    const downloadUrl = `/api/plugins/pcapPlay/reports/${taskId}/download`;

    // 创建隐藏的下载链接
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.style.display = 'none';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// ==================== 提示信息函数 ====================

// 显示错误信息
function showError(message) {
    showAlert(message, 'danger');
}

// 显示成功信息
function showSuccess(message) {
    showAlert(message, 'success');
}

// 显示警告信息
function showWarning(message) {
    showAlert(message, 'warning');
}

// 显示信息
function showInfo(message) {
    showAlert(message, 'info');
}

// 通用提示函数
function showAlert(message, type = 'info') {
    // 创建提示框容器
    let alertContainer = document.getElementById('alert-container');
    if (!alertContainer) {
        alertContainer = document.createElement('div');
        alertContainer.id = 'alert-container';
        alertContainer.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 99999;
            pointer-events: none;
            display: flex;
            flex-direction: column;
            gap: 12px;
            max-width: 400px;
        `;
        document.body.appendChild(alertContainer);
    }

    // 创建提示框
    const alertDiv = document.createElement('div');
    alertDiv.className = `custom-alert alert-${type}`;

    // 设置样式
    const alertStyles = {
        'success': {
            background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
            color: 'white',
            icon: '✓'
        },
        'warning': {
            background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',
            color: 'white',
            icon: '⚠'
        },
        'danger': {
            background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',
            color: 'white',
            icon: '✕'
        },
        'info': {
            background: 'linear-gradient(135deg, #3b82f6 0%, #2563eb 100%)',
            color: 'white',
            icon: 'ℹ'
        }
    };

    const style = alertStyles[type] || alertStyles['info'];

    alertDiv.style.cssText = `
        background: ${style.background};
        color: ${style.color};
        padding: 16px 20px;
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        font-size: 14px;
        font-weight: 500;
        line-height: 1.4;
        pointer-events: auto;
        cursor: pointer;
        transform: translateX(100%);
        opacity: 0;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;
        min-width: 300px;
        max-width: 380px;
        word-wrap: break-word;
    `;

    alertDiv.innerHTML = `
        <div style="display: flex; align-items: flex-start; gap: 12px;">
            <span style="font-size: 16px; flex-shrink: 0; margin-top: 1px;">${style.icon}</span>
            <div style="flex: 1;">
                <div style="font-weight: 600; margin-bottom: 2px;">
                    ${type === 'success' ? '成功' : type === 'warning' ? '警告' : type === 'danger' ? '错误' : '信息'}
                </div>
                <div style="opacity: 0.95; font-size: 13px;">${message}</div>
            </div>
            <button type="button" style="
                background: none;
                border: none;
                color: inherit;
                font-size: 18px;
                cursor: pointer;
                padding: 0;
                margin: 0;
                opacity: 0.7;
                transition: opacity 0.2s ease;
                flex-shrink: 0;
                width: 20px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
            " onclick="this.parentElement.parentElement.remove()">×</button>
        </div>
        <div style="
            position: absolute;
            bottom: 0;
            left: 0;
            height: 3px;
            background: rgba(255, 255, 255, 0.3);
            width: 100%;
            transform-origin: left;
            animation: alertProgress 4s linear forwards;
        "></div>
    `;

    // 添加进度条动画
    const style_element = document.createElement('style');
    style_element.textContent = `
        @keyframes alertProgress {
            from { transform: scaleX(1); }
            to { transform: scaleX(0); }
        }
    `;
    if (!document.querySelector('style[data-alert-styles]')) {
        style_element.setAttribute('data-alert-styles', 'true');
        document.head.appendChild(style_element);
    }

    alertContainer.appendChild(alertDiv);

    // 触发入场动画
    requestAnimationFrame(() => {
        alertDiv.style.transform = 'translateX(0)';
        alertDiv.style.opacity = '1';
    });

    // 4秒后自动消失
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.style.transform = 'translateX(100%)';
            alertDiv.style.opacity = '0';
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.parentNode.removeChild(alertDiv);
                }
            }, 300);
        }
    }, 4000);

    // 点击关闭
    alertDiv.addEventListener('click', () => {
        alertDiv.style.transform = 'translateX(100%)';
        alertDiv.style.opacity = '0';
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.parentNode.removeChild(alertDiv);
            }
        }, 300);
    });
}

// 点击模态框外部关闭
document.getElementById('taskDetailModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeTaskDetail();
    }
});

// ==================== 自动刷新功能 ====================

// 切换自动刷新
function toggleAutoRefresh() {
    const toggle = document.getElementById('autoRefreshToggle');
    autoRefreshEnabled = toggle.checked;

    if (autoRefreshEnabled) {
        startAutoRefresh();
    } else {
        stopAutoRefresh();
    }
}

// 开始自动刷新
function startAutoRefresh() {
    if (autoRefreshInterval) {
        clearInterval(autoRefreshInterval);
    }

    autoRefreshInterval = setInterval(() => {
        loadTaskList();
    }, 30000); // 30秒刷新一次
}

// 停止自动刷新
function stopAutoRefresh() {
    if (autoRefreshInterval) {
        clearInterval(autoRefreshInterval);
        autoRefreshInterval = null;
    }
}

// ==================== 复选框选择功能 ====================

// 切换全选
function toggleSelectAll() {
    const selectAllCheckbox = document.getElementById('selectAllTasks');
    const taskCheckboxes = document.querySelectorAll('.task-checkbox');

    taskCheckboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });

    updateBatchDeleteButton();
}

// 更新批量删除按钮状态
function updateBatchDeleteButton() {
    const selectedTasks = getSelectedTasks();
    const batchDeleteBtn = document.getElementById('batchDeleteBtn');
    const selectAllCheckbox = document.getElementById('selectAllTasks');
    const taskCheckboxes = document.querySelectorAll('.task-checkbox');

    // 更新批量删除按钮状态
    batchDeleteBtn.disabled = selectedTasks.length === 0;

    // 更新全选复选框状态
    if (selectedTasks.length === 0) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = false;
    } else if (selectedTasks.length === taskCheckboxes.length) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = true;
    } else {
        selectAllCheckbox.indeterminate = true;
        selectAllCheckbox.checked = false;
    }
}

// 获取选中的任务ID列表
function getSelectedTasks() {
    const selectedCheckboxes = document.querySelectorAll('.task-checkbox:checked');
    return Array.from(selectedCheckboxes).map(checkbox => checkbox.value);
}

// ==================== 删除任务功能 ====================

// 删除单个任务
function deleteTask(taskId) {
    const task = allTasks.find(t => t.id === taskId);
    if (!task) {
        showError('任务不存在');
        return;
    }

    // 允许删除正在进行中的任务
    let confirmMessage = `确定要删除任务 "${task.name}" 吗？此操作不可撤销。`;
    if (task.status === '进行中') {
        confirmMessage = `任务 "${task.name}" 正在进行中，确定要强制删除吗？此操作不可撤销。`;
    }

    if (!confirm(confirmMessage)) {
        return;
    }

    // 发送删除请求
    fetch(`/api/plugins/pcapPlay/tasks/${taskId}/delete`, {
        method: 'DELETE',
        headers: {
            'Content-Type': 'application/json'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccess('任务删除成功');
            loadTaskList(); // 重新加载任务列表
        } else {
            showError('删除任务失败: ' + data.message);
        }
    })
    .catch(error => {
        console.error('删除任务失败:', error);
        showError('删除任务失败: ' + error.message);
    });
}

// 批量删除任务
function batchDeleteTasks() {
    const selectedTaskIds = getSelectedTasks();

    if (selectedTaskIds.length === 0) {
        showError('请选择要删除的任务');
        return;
    }

    // 检查是否有正在运行的任务
    const runningTasks = selectedTaskIds.filter(taskId => {
        const task = allTasks.find(t => t.id === taskId);
        return task && task.status === '进行中';
    });

    let confirmMessage = `确定要删除选中的 ${selectedTaskIds.length} 个任务吗？此操作不可撤销。`;
    if (runningTasks.length > 0) {
        confirmMessage = `选中的任务中有 ${runningTasks.length} 个正在进行中，确定要强制删除选中的 ${selectedTaskIds.length} 个任务吗？此操作不可撤销。`;
    }

    if (!confirm(confirmMessage)) {
        return;
    }

    // 批量删除任务
    let deletedCount = 0;
    let failedCount = 0;
    const totalCount = selectedTaskIds.length;

    // 显示进度
    const progressMessage = `正在删除任务 (0/${totalCount})...`;
    showInfo(progressMessage);

    // 逐个删除任务
    const deletePromises = selectedTaskIds.map((taskId, index) => {
        return fetch(`/api/plugins/pcapPlay/tasks/${taskId}/delete`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                deletedCount++;
            } else {
                failedCount++;
                console.error(`删除任务 ${taskId} 失败:`, data.message);
            }

            // 更新进度
            const progress = deletedCount + failedCount;
            if (progress < totalCount) {
                showInfo(`正在删除任务 (${progress}/${totalCount})...`);
            }
        })
        .catch(error => {
            failedCount++;
            console.error(`删除任务 ${taskId} 失败:`, error);
        });
    });

    // 等待所有删除操作完成
    Promise.all(deletePromises).then(() => {
        // 显示结果
        if (failedCount === 0) {
            showSuccess(`成功删除 ${deletedCount} 个任务`);
        } else if (deletedCount === 0) {
            showError(`删除失败，${failedCount} 个任务删除失败`);
        } else {
            showWarning(`部分删除成功：${deletedCount} 个成功，${failedCount} 个失败`);
        }

        // 重新加载任务列表
        loadTaskList();

        // 清除选择状态
        document.getElementById('selectAllTasks').checked = false;
        updateBatchDeleteButton();
    });
}
</script>
{% endblock %}

{% block extra_css %}
<!-- 引入通用样式框架 -->
<link href="{{ url_for('static', filename='plugins/pcapPlay/css/pcap-replay-common.css') }}" rel="stylesheet">
{% endblock %}
