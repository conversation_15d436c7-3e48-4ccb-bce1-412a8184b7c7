{% extends "base.html" %}

{% block title %}新建测试任务 - {{ pluginInfo.displayName }}{% endblock %}

{% block content %}
<div class="pcap-replay-container">
    <!-- 页面头部 -->
    <div class="page-header">
        <div class="header-content">
            <div class="header-title">
                <i class="fas fa-play-circle me-3"></i>
            </div>
            <div class="header-actions">
                <a class="action-btn" href="/plugins/pcapPlay/">
                    <i class="fas fa-cog"></i>
                    <span>系统设置</span>
                </a>
                <a class="action-btn active" href="/plugins/pcapPlay/create-task">
                    <i class="fas fa-plus-circle"></i>
                    <span>新建任务</span>
                </a>
                <a class="action-btn" href="/plugins/pcapPlay/task-results">
                    <i class="fas fa-list-alt"></i>
                    <span>任务结果</span>
                </a>
                <a class="action-btn" href="/plugins/pcapPlay/runtime-logs">
                    <i class="fas fa-file-alt"></i>
                    <span>运行日志</span>
                </a>
            </div>
        </div>
    </div>

    <!-- 任务创建面板 -->
    <div class="content-panels">
        <div class="panel task-panel">

            <div class="panel-header">
                <div class="panel-icon">
                    <i class="fas fa-plus-circle"></i>
                </div>
                <div class="panel-title">
                    <h3>新建测试任务</h3>
                    <p>配置任务参数并上传测试文件</p>
                </div>
            </div>
            <div class="panel-body">
                <form id="createTaskForm" class="form-section">
                    <!-- 任务名称 -->
                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-tag"></i>
                            <span>任务名称</span>
                        </label>
                        <input type="text"
                               class="form-input"
                               id="taskName"
                               placeholder="请输入任务名称"
                               required>
                        <div class="form-hint">请输入一个有意义的任务名称，便于后续识别和管理</div>
                    </div>

                    <!-- 测试报文文件 -->
                    <div class="form-group">
                        <label class="form-label">
                            <i class="fas fa-file-upload"></i>
                            <span>测试报文文件</span>
                        </label>

                        <!-- 上传方式选择 -->
                        <div class="upload-mode-selector">
                            <div class="mode-option">
                                <input type="radio" id="fileMode" name="uploadMode" value="file" checked>
                                <label for="fileMode">
                                    <i class="fas fa-file"></i>
                                    <span>选择文件</span>
                                </label>
                            </div>
                            <div class="mode-option">
                                <input type="radio" id="folderMode" name="uploadMode" value="folder">
                                <label for="folderMode">
                                    <i class="fas fa-folder"></i>
                                    <span>选择文件夹</span>
                                </label>
                            </div>
                        </div>

                        <div class="file-upload-area" id="fileUploadArea">
                            <div class="upload-content">
                                <i class="fas fa-cloud-upload-alt upload-icon"></i>
                                <p class="upload-text" id="uploadText">点击选择文件或拖拽文件到此处</p>
                                <p class="upload-hint" id="uploadHint">支持格式：.pcap, .pcapng, .cap（可选择多个文件）</p>
                            </div>
                            <input type="file"
                                   id="fileInput"
                                   multiple
                                   accept=".pcap,.pcapng,.cap"
                                   style="display: none;">
                            <input type="file"
                                   id="folderInput"
                                   webkitdirectory
                                   multiple
                                   style="display: none;">
                        </div>
                        <div id="fileList" class="file-list"></div>
                    </div>
                </form>
            </div>
            <div class="panel-footer">
                <button type="submit"
                        class="btn-primary"
                        id="startTestBtn"
                        form="createTaskForm">
                    <i class="fas fa-play"></i>
                    <span>开始测试</span>
                </button>
            </div>
        </div>
    </div>

    <!-- 操作按钮区域 -->
    <div class="action-section" style="display: none;">
        <!-- 隐藏，因为按钮已经在面板底部 -->
    </div>
</div>
{% endblock %}

{% block extra_css %}
<!-- 引入通用样式框架 -->
<link href="{{ url_for('static', filename='plugins/pcapPlay/css/pcap-replay-common.css') }}" rel="stylesheet">

<style>
/* 报文回放工具 - 新建任务页面特定样式 */

/* 任务面板特定样式 */
.task-panel {
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
}



/* 上传方式选择器样式 */
.upload-mode-selector {
    display: flex;
    gap: 16px;
    margin-bottom: 16px;
    padding: 8px;
    background: #f8fafc;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
}

.mode-option {
    flex: 1;
}

.mode-option input[type="radio"] {
    display: none;
}

.mode-option label {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 12px 16px;
    background: white;
    border: 2px solid #e2e8f0;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    font-weight: 500;
    color: #64748b;
}

.mode-option label:hover {
    border-color: #667eea;
    background: #f1f5f9;
}

.mode-option input[type="radio"]:checked + label {
    background: #667eea;
    border-color: #667eea;
    color: white;
}

.mode-option label i {
    margin-right: 8px;
    font-size: 16px;
}

/* 文件上传区域样式 */
.file-upload-area {
    border: 2px dashed #d1d5db;
    border-radius: 12px;
    padding: 32px 20px;
    text-align: center;
    background: #f9fafb;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
}

.file-upload-area:hover {
    border-color: #667eea;
    background: #eff6ff;
}

.file-upload-area.dragover {
    border-color: #667eea;
    background: #dbeafe;
    transform: scale(1.02);
}

.upload-icon {
    font-size: 40px;
    color: #667eea;
    margin-bottom: 12px;
}

.upload-text {
    color: #1f2937;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 6px;
}

.upload-hint {
    color: #6b7280;
    font-size: 12px;
    margin-bottom: 0;
}

/* 文件列表样式 */
.file-list {
    max-height: 180px;
    overflow-y: auto;
    margin-top: 12px;
}

.file-item {
    background: white;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 10px 12px;
    margin-bottom: 6px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    transition: all 0.3s ease;
}

.file-item:hover {
    background: #f9fafb;
    border-color: #667eea;
}

.file-info {
    display: flex;
    align-items: center;
    flex: 1;
}

.file-icon {
    color: #667eea;
    margin-right: 10px;
    font-size: 16px;
}

.file-name {
    color: #1f2937;
    font-weight: 500;
    margin-right: 10px;
    font-size: 13px;
}

.file-size {
    color: #6b7280;
    font-size: 11px;
}

.file-remove {
    background: none;
    border: none;
    color: #ef4444;
    cursor: pointer;
    padding: 4px 6px;
    border-radius: 4px;
    transition: all 0.3s ease;
    font-size: 14px;
}

.file-remove:hover {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
}




</style>

<script>
document.addEventListener('DOMContentLoaded', function() {


    const fileInput = document.getElementById('fileInput');
    const folderInput = document.getElementById('folderInput');
    const fileUploadArea = document.getElementById('fileUploadArea');
    const fileList = document.getElementById('fileList');
    const createTaskForm = document.getElementById('createTaskForm');
    const startTestBtn = document.getElementById('startTestBtn');
    const uploadText = document.getElementById('uploadText');
    const uploadHint = document.getElementById('uploadHint');
    const fileModeRadio = document.getElementById('fileMode');
    const folderModeRadio = document.getElementById('folderMode');

    let selectedFiles = [];
    let currentUploadMode = 'file';

    // 上传方式切换事件
    fileModeRadio.addEventListener('change', function() {
        if (this.checked) {
            switchUploadMode('file');
        }
    });

    folderModeRadio.addEventListener('change', function() {
        if (this.checked) {
            switchUploadMode('folder');
        }
    });

    // 切换上传方式
    function switchUploadMode(mode) {
        currentUploadMode = mode;
        if (mode === 'file') {
            uploadText.textContent = '点击选择文件或拖拽文件到此处';
            uploadHint.textContent = '支持格式：.pcap, .pcapng, .cap（可选择多个文件）';
        } else {
            uploadText.textContent = '点击选择文件夹或拖拽文件夹到此处';
            uploadHint.textContent = '将自动扫描文件夹中的报文文件（.pcap, .pcapng, .cap）';
        }
        // 清空已选择的文件
        selectedFiles = [];
        updateFileList();
        updateStartButton();
    }

    // 文件上传区域点击事件
    fileUploadArea.addEventListener('click', function() {
        if (currentUploadMode === 'file') {
            fileInput.click();
        } else {
            folderInput.click();
        }
    });

    // 文件选择事件
    fileInput.addEventListener('change', function(e) {
        handleFiles(e.target.files);
    });

    // 文件夹选择事件
    folderInput.addEventListener('change', function(e) {
        handleFolderFiles(e.target.files);
    });

    // 拖拽事件
    fileUploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        fileUploadArea.classList.add('dragover');
    });

    fileUploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        fileUploadArea.classList.remove('dragover');
    });

    fileUploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        fileUploadArea.classList.remove('dragover');

        // 检查是否拖拽的是文件夹
        const items = e.dataTransfer.items;
        if (items && items.length > 0) {
            // 检查是否有文件夹
            let hasFolder = false;
            for (let item of items) {
                if (item.webkitGetAsEntry && item.webkitGetAsEntry().isDirectory) {
                    hasFolder = true;
                    break;
                }
            }

            if (hasFolder) {
                // 切换到文件夹模式
                folderModeRadio.checked = true;
                switchUploadMode('folder');
                handleDroppedFolder(items);
            } else {
                handleFiles(e.dataTransfer.files);
            }
        } else {
            handleFiles(e.dataTransfer.files);
        }
    });

    // 处理拖拽的文件夹
    function handleDroppedFolder(items) {
        const files = [];
        let pendingPromises = [];

        for (let item of items) {
            const entry = item.webkitGetAsEntry();
            if (entry) {
                pendingPromises.push(traverseFileTree(entry, files));
            }
        }

        Promise.all(pendingPromises).then(() => {
            handleFolderFiles(files);
        });
    }

    // 遍历文件树
    function traverseFileTree(item, files, path = '') {
        return new Promise((resolve) => {
            if (item.isFile) {
                item.file((file) => {
                    if (isValidFile(file)) {
                        // 添加相对路径信息
                        file.relativePath = path + file.name;
                        files.push(file);
                    }
                    resolve();
                });
            } else if (item.isDirectory) {
                const dirReader = item.createReader();
                dirReader.readEntries((entries) => {
                    const promises = entries.map(entry =>
                        traverseFileTree(entry, files, path + item.name + '/')
                    );
                    Promise.all(promises).then(() => resolve());
                });
            } else {
                resolve();
            }
        });
    }

    // 处理文件夹中的文件
    function handleFolderFiles(files) {
        let validFileCount = 0;
        let invalidFileCount = 0;

        for (let file of files) {
            if (isValidFile(file)) {
                selectedFiles.push(file);
                validFileCount++;
            } else {
                invalidFileCount++;
            }
        }

        updateFileList();
        updateStartButton();

        if (validFileCount > 0) {
            showAlert(`成功添加 ${validFileCount} 个报文文件${invalidFileCount > 0 ? `，跳过 ${invalidFileCount} 个非报文文件` : ''}`, 'success');
        } else if (invalidFileCount > 0) {
            showAlert('文件夹中没有找到有效的报文文件（.pcap, .pcapng, .cap）', 'warning');
        }
    }

    // 处理文件
    function handleFiles(files) {
        for (let file of files) {
            if (isValidFile(file)) {
                selectedFiles.push(file);
            } else {
                showAlert(`文件 ${file.name} 格式不支持，请选择 .pcap, .pcapng 或 .cap 文件`, 'warning');
            }
        }
        updateFileList();
        updateStartButton();
    }

    // 验证文件格式
    function isValidFile(file) {
        const validExtensions = ['.pcap', '.pcapng', '.cap'];
        const fileName = file.name.toLowerCase();
        return validExtensions.some(ext => fileName.endsWith(ext));
    }

    // 更新文件列表显示
    function updateFileList() {
        fileList.innerHTML = '';
        selectedFiles.forEach((file, index) => {
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';

            // 显示文件名（如果是文件夹模式，显示相对路径）
            const displayName = file.relativePath || file.name;

            fileItem.innerHTML = `
                <div class="file-info">
                    <i class="fas fa-file-code file-icon"></i>
                    <span class="file-name" title="${displayName}">${displayName}</span>
                    <span class="file-size">${formatFileSize(file.size)}</span>
                </div>
                <button type="button" class="file-remove" onclick="removeFile(${index})">
                    <i class="fas fa-times"></i>
                </button>
            `;
            fileList.appendChild(fileItem);
        });
    }

    // 移除文件
    window.removeFile = function(index) {
        selectedFiles.splice(index, 1);
        updateFileList();
        updateStartButton();
    };

    // 格式化文件大小
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // 更新开始按钮状态
    function updateStartButton() {
        const taskName = document.getElementById('taskName').value.trim();
        const hasFiles = selectedFiles.length > 0;
        startTestBtn.disabled = !taskName || !hasFiles;
    }

    // 任务名称输入事件
    document.getElementById('taskName').addEventListener('input', updateStartButton);

    // 表单提交事件
    createTaskForm.addEventListener('submit', function(e) {
        e.preventDefault();
        createTask();
    });

    // 创建任务
    function createTask() {
        const taskName = document.getElementById('taskName').value.trim();
        
        if (!taskName) {
            showAlert('请输入任务名称', 'warning');
            return;
        }

        if (selectedFiles.length === 0) {
            showAlert('请选择至少一个测试文件', 'warning');
            return;
        }

        // 禁用按钮，显示加载状态
        startTestBtn.disabled = true;
        startTestBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>创建中...';

        // 创建FormData
        const formData = new FormData();
        formData.append('taskName', taskName);
        
        selectedFiles.forEach(file => {
            formData.append('files', file);
        });

        // 发送请求
        fetch('/api/plugins/pcapPlay/tasks', {
            method: 'POST',
            body: formData
        })
        .then(response => {
            console.log('Response status:', response.status);
            console.log('Response headers:', response.headers);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            return response.json();
        })
        .then(data => {
            console.log('Response data:', data);
            if (data.success) {
                // 使用服务器返回的详细消息
                let successMessage = data.message || '任务创建成功！';

                // 如果有文件统计信息，显示更详细的信息
                if (data.fileStats) {
                    const stats = data.fileStats;
                    if (stats.invalidFiles > 0) {
                        successMessage += `\n成功处理 ${stats.validFiles} 个报文文件，跳过 ${stats.invalidFiles} 个非报文文件`;
                    }
                }

                successMessage += '\n正在跳转到任务结果页面...';
                showAlert(successMessage, 'success');

                // 跳转到任务结果页面
                setTimeout(() => {
                    window.location.href = '/plugins/pcapPlay/task-results';
                }, 2000);
            } else {
                showAlert('任务创建失败: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            console.error('创建任务失败:', error);
            console.error('Error details:', {
                name: error.name,
                message: error.message,
                stack: error.stack
            });

            let errorMessage = '任务创建失败';
            if (error.message.includes('Failed to fetch')) {
                errorMessage += ': 网络连接失败，请检查网络连接或稍后重试';
            } else if (error.message.includes('HTTP error')) {
                errorMessage += ': 服务器响应错误 (' + error.message + ')';
            } else {
                errorMessage += ': ' + error.message;
            }

            showAlert(errorMessage, 'danger');
        })
        .finally(() => {
            // 恢复按钮状态
            startTestBtn.disabled = false;
            startTestBtn.innerHTML = '<i class="fas fa-play me-2"></i>开始测试';
        });
    }

    // 显示提示信息 - 使用优化版本
    function showAlert(message, type = 'info') {
        // 创建提示框容器
        let alertContainer = document.getElementById('alert-container');
        if (!alertContainer) {
            alertContainer = document.createElement('div');
            alertContainer.id = 'alert-container';
            alertContainer.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 99999;
                pointer-events: none;
                display: flex;
                flex-direction: column;
                gap: 12px;
                max-width: 400px;
            `;
            document.body.appendChild(alertContainer);
        }

        // 创建提示框
        const alertDiv = document.createElement('div');
        alertDiv.className = `custom-alert alert-${type}`;

        // 设置样式
        const alertStyles = {
            'success': {
                background: 'linear-gradient(135deg, #10b981 0%, #059669 100%)',
                color: 'white',
                icon: '✓'
            },
            'warning': {
                background: 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',
                color: 'white',
                icon: '⚠'
            },
            'danger': {
                background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',
                color: 'white',
                icon: '✕'
            },
            'info': {
                background: 'linear-gradient(135deg, #3b82f6 0%, #2563eb 100%)',
                color: 'white',
                icon: 'ℹ'
            }
        };

        const style = alertStyles[type] || alertStyles['info'];

        alertDiv.style.cssText = `
            background: ${style.background};
            color: ${style.color};
            padding: 16px 20px;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            font-size: 14px;
            font-weight: 500;
            line-height: 1.4;
            pointer-events: auto;
            cursor: pointer;
            transform: translateX(100%);
            opacity: 0;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            overflow: hidden;
            min-width: 300px;
            max-width: 380px;
            word-wrap: break-word;
        `;

        alertDiv.innerHTML = `
            <div style="display: flex; align-items: flex-start; gap: 12px;">
                <span style="font-size: 16px; flex-shrink: 0; margin-top: 1px;">${style.icon}</span>
                <div style="flex: 1;">
                    <div style="font-weight: 600; margin-bottom: 2px;">
                        ${type === 'success' ? '成功' : type === 'warning' ? '警告' : type === 'danger' ? '错误' : '信息'}
                    </div>
                    <div style="opacity: 0.95; font-size: 13px;">${message}</div>
                </div>
                <button type="button" style="
                    background: none;
                    border: none;
                    color: inherit;
                    font-size: 18px;
                    cursor: pointer;
                    padding: 0;
                    margin: 0;
                    opacity: 0.7;
                    transition: opacity 0.2s ease;
                    flex-shrink: 0;
                    width: 20px;
                    height: 20px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                " onclick="this.parentElement.parentElement.remove()">×</button>
            </div>
            <div style="
                position: absolute;
                bottom: 0;
                left: 0;
                height: 3px;
                background: rgba(255, 255, 255, 0.3);
                width: 100%;
                transform-origin: left;
                animation: alertProgress 4s linear forwards;
            "></div>
        `;

        // 添加进度条动画
        const style_element = document.createElement('style');
        style_element.textContent = `
            @keyframes alertProgress {
                from { transform: scaleX(1); }
                to { transform: scaleX(0); }
            }
        `;
        if (!document.querySelector('style[data-alert-styles]')) {
            style_element.setAttribute('data-alert-styles', 'true');
            document.head.appendChild(style_element);
        }

        alertContainer.appendChild(alertDiv);

        // 触发入场动画
        requestAnimationFrame(() => {
            alertDiv.style.transform = 'translateX(0)';
            alertDiv.style.opacity = '1';
        });

        // 4秒后自动消失
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.style.transform = 'translateX(100%)';
                alertDiv.style.opacity = '0';
                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.parentNode.removeChild(alertDiv);
                    }
                }, 300);
            }
        }, 4000);

        // 点击关闭
        alertDiv.addEventListener('click', () => {
            alertDiv.style.transform = 'translateX(100%)';
            alertDiv.style.opacity = '0';
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.parentNode.removeChild(alertDiv);
                }
            }, 300);
        });
    }

    // 初始化按钮状态
    updateStartButton();
});
</script>
{% endblock %}
